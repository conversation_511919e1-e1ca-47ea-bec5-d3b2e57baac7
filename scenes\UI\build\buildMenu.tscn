[gd_scene load_steps=55 format=3 uid="uid://c1r8ujqauaqwx"]

[ext_resource type="Script" uid="uid://c17c8idksyp1j" path="res://scenes/UI/build/BuildMenu.cs" id="1_buildmenu"]
[ext_resource type="Texture2D" uid="uid://bgpd0wfpx3kvj" path="res://resources/solaria/UI/inventory/inventory_item_single_slot.png" id="2_3na7a"]
[ext_resource type="Texture2D" uid="uid://cyvhsyv6xia6m" path="res://resources/solaria/UI/build/build_panel.png" id="2_fdrk1"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="3_3na7a"]
[ext_resource type="Texture2D" uid="uid://bidfkpfge410f" path="res://resources/solaria/buildings/anvil_icon.png" id="4_fdrk1"]
[ext_resource type="Texture2D" uid="uid://bdscl0odejahl" path="res://resources/solaria/resources/resource_stone.png" id="5_pd31f"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="6_0qcxk"]
[ext_resource type="Texture2D" uid="uid://486dt68qu54c" path="res://resources/solaria/resources/resource_wood.png" id="6_bkysm"]
[ext_resource type="Texture2D" uid="uid://qtnmf3qqfvr5" path="res://resources/solaria/UI/build/buildButton.png" id="8_q2at4"]
[ext_resource type="Texture2D" uid="uid://bsrmiu50aadu8" path="res://resources/solaria/buildings/bridge.png" id="9_bkysm"]
[ext_resource type="Texture2D" uid="uid://rbmx7uwcpffw" path="res://resources/solaria/resources/resource_plank.png" id="10_plank"]
[ext_resource type="Texture2D" uid="uid://d0jb64dssfvc7" path="res://resources/solaria/resources/resource_beam.png" id="11_beam"]
[ext_resource type="Texture2D" uid="uid://chqakc1gv5pov" path="res://resources/solaria/buildings/animated/campfire.png" id="12_agrao"]
[ext_resource type="Texture2D" uid="uid://bb07e8p3clukd" path="res://resources/solaria/resources/stone2_brick.png" id="13_v5thu"]
[ext_resource type="Texture2D" uid="uid://da2hrddwvluf0" path="res://resources/solaria/buildings/Forge1Icon.png" id="13_vf62o"]
[ext_resource type="Texture2D" uid="uid://cll3yjfn0tnk5" path="res://resources/solaria/resources/stone_brick.png" id="14_m63xl"]
[ext_resource type="Texture2D" uid="uid://bwhuvj67bfv7j" path="res://resources/solaria/buildings/Forge2Icon.png" id="14_rk4c3"]
[ext_resource type="Texture2D" uid="uid://dswuej2j7npsy" path="res://resources/solaria/buildings/Forge3Icon.png" id="15_il0uu"]
[ext_resource type="Texture2D" uid="uid://cqdawrs36uqes" path="res://resources/solaria/buildings/Forge4Icon.png" id="16_ii6d2"]
[ext_resource type="Texture2D" uid="uid://cvcjcrg4rarkj" path="res://resources/solaria/resources/copper_bar_resource.png" id="16_pqqmx"]
[ext_resource type="Texture2D" uid="uid://dxnkb2dydn4p4" path="res://resources/solaria/resources/iron_bar_resource.png" id="17_5lmll"]
[ext_resource type="Texture2D" uid="uid://be08gmk5w8bhc" path="res://resources/solaria/buildings/grindstoneIcon.png" id="17_il0uu"]
[ext_resource type="Texture2D" uid="uid://c7mgr0rih0doa" path="res://resources/solaria/resources/gold.png" id="18_hee3s"]
[ext_resource type="Texture2D" uid="uid://b13la3f0ea68l" path="res://resources/solaria/buildings/workbench_icon.png" id="18_ii6d2"]
[ext_resource type="Texture2D" uid="uid://baxrxbu5b0l5o" path="res://resources/solaria/resources/indigosium_bar_resource.png" id="20_bmj2c"]
[ext_resource type="Texture2D" uid="uid://bfmpggdjxfqb2" path="res://resources/solaria/resources/gole_bar_resource.png" id="21_m7jsl"]
[ext_resource type="Texture2D" uid="uid://bqt7ide47iyj4" path="res://resources/solaria/resources/mithril.png" id="22_qa7ff"]
[ext_resource type="Texture2D" uid="uid://c01nycw7h0qr" path="res://resources/solaria/resources/erithrydium_bar_resource.png" id="24_2b3r4"]
[ext_resource type="Texture2D" uid="uid://dq020f2g4eeoe" path="res://resources/solaria/resources/mithril_bar_resource.png" id="25_baed2"]
[ext_resource type="Texture2D" uid="uid://0piwvdteioos" path="res://resources/solaria/resources/adamantite.png" id="26_mp1xg"]

[sub_resource type="Animation" id="Animation_iw7eh"]
resource_name = "Close"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.09),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_r0oy0"]
resource_name = "Open"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_cna2i"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_v0ypl"]
_data = {
&"Close": SubResource("Animation_iw7eh"),
&"Open": SubResource("Animation_r0oy0"),
&"RESET": SubResource("Animation_cna2i")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_yu1h3"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_v5thu"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_m63xl"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_qojrk"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_raup1"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_inwf0"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_koj3k"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_s1sgt"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_0fxak"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_pd31f"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_bkysm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_q2at4"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_rdlrw"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_agrao"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_vf62o"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_o3kbu"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_cu0la"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_rk4c3"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_il0uu"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ii6d2"]

[node name="BuildMenu" type="CanvasLayer"]
script = ExtResource("1_buildmenu")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_v0ypl")
}

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="Control"]
visible = false
scale = Vector2(0.95, 0.95)
texture = ExtResource("2_fdrk1")

[node name="ScrollContainer" type="ScrollContainer" parent="Control/Panel"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -92.0
offset_top = -116.0
offset_right = -99.0
offset_bottom = -124.0
grow_horizontal = 2
grow_vertical = 2
vertical_scroll_mode = 3

[node name="VBoxContainer" type="VBoxContainer" parent="Control/Panel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ItemListAnvil" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_yu1h3")
theme_override_styles/panel = SubResource("StyleBoxFlat_v5thu")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_m63xl")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_qojrk")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_raup1")
theme_override_styles/selected = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_koj3k")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_s1sgt")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_0fxak")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_3na7a")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("4_fdrk1")

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil"]
position = Vector2(141.32, 27.1579)
texture = ExtResource("5_pd31f")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil"]
position = Vector2(141.32, 14.5263)
texture = ExtResource("6_bkysm")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 21.2632
offset_right = 210.737
offset_bottom = 38.2632
scale = Vector2(0.73, 0.73)
text = "5"
horizontal_alignment = 0

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 8.6316
offset_right = 210.737
offset_bottom = 25.6316
scale = Vector2(0.73, 0.73)
text = "5"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_q2at4")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = -1.0
offset_right = 178.0
offset_bottom = 21.0
scale = Vector2(0.73, 0.73)
text = "ANVIL_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "ANVIL_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")

[node name="ItemListCampfire" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_yu1h3")
theme_override_styles/panel = SubResource("StyleBoxFlat_v5thu")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_m63xl")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_qojrk")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_raup1")
theme_override_styles/selected = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_koj3k")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_s1sgt")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_0fxak")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_3na7a")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("12_agrao")
hframes = 8
vframes = 2
frame = 8

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire"]
position = Vector2(141.32, 27.1579)
texture = ExtResource("5_pd31f")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire"]
position = Vector2(141.32, 14.5263)
texture = ExtResource("6_bkysm")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 21.2632
offset_right = 210.737
offset_bottom = 38.2632
scale = Vector2(0.73, 0.73)
text = "2"
horizontal_alignment = 0

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 8.6316
offset_right = 210.737
offset_bottom = 25.6316
scale = Vector2(0.73, 0.73)
text = "5"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_q2at4")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_right = 178.0
offset_bottom = 19.0
scale = Vector2(0.73, 0.73)
text = "CAMPFIRE_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "CAMPFIRE_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")

[node name="ItemListBridge" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_yu1h3")
theme_override_styles/panel = SubResource("StyleBoxFlat_v5thu")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_m63xl")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_qojrk")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_raup1")
theme_override_styles/selected = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_koj3k")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_s1sgt")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_0fxak")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_3na7a")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("9_bkysm")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge"]
position = Vector2(141.32, 18.9474)
texture = ExtResource("10_plank")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 13.0527
offset_right = 210.737
offset_bottom = 30.0527
scale = Vector2(0.73, 0.73)
text = "5"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_q2at4")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_right = 178.0
offset_bottom = 18.0
scale = Vector2(0.73, 0.73)
text = "BRIDGE_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "BRIDGE_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")

[node name="ItemListFurnace1" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_yu1h3")
theme_override_styles/panel = SubResource("StyleBoxFlat_v5thu")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_m63xl")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_qojrk")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_raup1")
theme_override_styles/selected = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_koj3k")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_s1sgt")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_0fxak")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_3na7a")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("13_vf62o")

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1"]
position = Vector2(141.32, 27.1579)
texture = ExtResource("13_v5thu")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1"]
position = Vector2(141.32, 14.5263)
texture = ExtResource("14_m63xl")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 122.526
offset_top = 21.8948
offset_right = 206.526
offset_bottom = 38.8948
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 122.526
offset_top = 9.26315
offset_right = 206.526
offset_bottom = 26.2632
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_q2at4")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_right = 178.0
offset_bottom = 19.0
scale = Vector2(0.73, 0.73)
text = "FURNACE1_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "FURNACE1_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")

[node name="ItemListWorkbench" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_yu1h3")
theme_override_styles/panel = SubResource("StyleBoxFlat_v5thu")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_m63xl")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_qojrk")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_raup1")
theme_override_styles/selected = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_koj3k")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_s1sgt")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_0fxak")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_3na7a")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("18_ii6d2")

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench"]
position = Vector2(142.373, 25.8947)
texture = ExtResource("14_m63xl")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench"]
position = Vector2(142.373, 13.2631)
texture = ExtResource("10_plank")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 123.579
offset_top = 19.7895
offset_right = 207.579
offset_bottom = 36.7895
scale = Vector2(0.73, 0.73)
text = "10"
horizontal_alignment = 0

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 123.579
offset_top = 7.15789
offset_right = 207.579
offset_bottom = 24.1579
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_q2at4")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_right = 178.0
offset_bottom = 19.0
scale = Vector2(0.73, 0.73)
text = "WORKBENCH_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "WORKBENCH_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")

[node name="ItemListGrindstone" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_yu1h3")
theme_override_styles/panel = SubResource("StyleBoxFlat_v5thu")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_m63xl")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_qojrk")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_raup1")
theme_override_styles/selected = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_koj3k")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_s1sgt")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_0fxak")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_3na7a")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("17_il0uu")

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone"]
position = Vector2(142.373, 20.421)
texture = ExtResource("14_m63xl")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone"]
position = Vector2(142.373, 7.78943)
texture = ExtResource("11_beam")

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone"]
position = Vector2(142.373, 33.6842)
texture = ExtResource("13_v5thu")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 124.632
offset_top = 14.5263
offset_right = 208.632
offset_bottom = 31.5263
scale = Vector2(0.73, 0.73)
text = "10"
horizontal_alignment = 0

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 123.579
offset_top = 1.89473
offset_right = 207.579
offset_bottom = 18.8947
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 124.632
offset_top = 27.7894
offset_right = 208.632
offset_bottom = 44.7894
scale = Vector2(0.73, 0.73)
text = "10"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_q2at4")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_right = 178.0
offset_bottom = 19.0
scale = Vector2(0.73, 0.73)
text = "GRINDSTONE_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "GRINDSTONE_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")

[node name="ItemListSeedMaker" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_yu1h3")
theme_override_styles/panel = SubResource("StyleBoxFlat_v5thu")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_m63xl")
theme_override_styles/selected_mirrored = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_koj3k")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_s1sgt")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_0fxak")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_3na7a")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("18_ii6d2")

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
position = Vector2(142.373, 25.8947)
texture = ExtResource("14_m63xl")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
position = Vector2(142.373, 13.2631)
texture = ExtResource("10_plank")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 123.579
offset_top = 19.7895
offset_right = 207.579
offset_bottom = 36.7895
scale = Vector2(0.73, 0.73)
text = "10"
horizontal_alignment = 0

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 123.579
offset_top = 7.15789
offset_right = 207.579
offset_bottom = 24.1579
scale = Vector2(0.73, 0.73)
text = "15"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_q2at4")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_right = 178.0
offset_bottom = 19.0
scale = Vector2(0.73, 0.73)
text = "SEEDMAKER_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "SEEDMAKER_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")

[node name="ItemListFurnace2" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_yu1h3")
theme_override_styles/panel = SubResource("StyleBoxFlat_v5thu")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_m63xl")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_qojrk")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_raup1")
theme_override_styles/selected = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_koj3k")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_s1sgt")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_0fxak")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_3na7a")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("14_rk4c3")

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2"]
position = Vector2(145.531, 20.6316)
texture = ExtResource("16_pqqmx")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2"]
position = Vector2(145.531, 7.99997)
texture = ExtResource("17_5lmll")

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2"]
position = Vector2(144.632, 33.6842)
texture = ExtResource("18_hee3s")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 14.5263
offset_right = 210.737
offset_bottom = 31.5263
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 26.9474
offset_right = 210.737
offset_bottom = 43.9474
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 1.89473
offset_right = 210.737
offset_bottom = 18.8947
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_q2at4")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_right = 178.0
offset_bottom = 19.0
scale = Vector2(0.73, 0.73)
text = "FURNACE2_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "FURNACE2_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")

[node name="ItemListFurnace3" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_yu1h3")
theme_override_styles/panel = SubResource("StyleBoxFlat_v5thu")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_m63xl")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_qojrk")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_raup1")
theme_override_styles/selected = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_koj3k")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_s1sgt")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_0fxak")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_3na7a")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("15_il0uu")

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3"]
position = Vector2(144.478, 20.6316)
texture = ExtResource("20_bmj2c")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3"]
position = Vector2(144.478, 7.99997)
texture = ExtResource("21_m7jsl")

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3"]
position = Vector2(144.478, 33.0526)
texture = ExtResource("22_qa7ff")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 14.7369
offset_right = 210.737
offset_bottom = 31.7369
scale = Vector2(0.73, 0.73)
text = "20	"
horizontal_alignment = 0

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 2.10527
offset_right = 210.737
offset_bottom = 19.1053
scale = Vector2(0.73, 0.73)
text = "20	"
horizontal_alignment = 0

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 27.1579
offset_right = 210.737
offset_bottom = 44.1579
scale = Vector2(0.73, 0.73)
text = "20	"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_q2at4")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_right = 178.0
offset_bottom = 19.0
scale = Vector2(0.73, 0.73)
text = "FURNACE3_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "FURNACE3_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")

[node name="ItemListFurnace4" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_yu1h3")
theme_override_styles/panel = SubResource("StyleBoxFlat_v5thu")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_m63xl")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_qojrk")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_raup1")
theme_override_styles/selected = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_koj3k")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_s1sgt")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_0fxak")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_3na7a")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("16_ii6d2")

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4"]
position = Vector2(145.263, 19.5789)
texture = ExtResource("24_2b3r4")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4"]
position = Vector2(145.263, 6.94734)
texture = ExtResource("25_baed2")

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4"]
position = Vector2(145.263, 32)
texture = ExtResource("26_mp1xg")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 13.6842
offset_right = 210.737
offset_bottom = 30.6842
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 1.05264
offset_right = 210.737
offset_bottom = 18.0526
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 26.1053
offset_right = 210.737
offset_bottom = 43.1053
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_q2at4")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_right = 178.0
offset_bottom = 19.0
scale = Vector2(0.73, 0.73)
text = "FURNACE4_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "FURNACE4_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")

[node name="ItemListSeedMaker" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_yu1h3")
theme_override_styles/panel = SubResource("StyleBoxFlat_v5thu")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_m63xl")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_qojrk")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_raup1")
theme_override_styles/selected = SubResource("StyleBoxEmpty_inwf0")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_koj3k")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_s1sgt")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_0fxak")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_3na7a")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("16_ii6d2")

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
position = Vector2(145.263, 19.5789)
texture = ExtResource("24_2b3r4")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
position = Vector2(145.263, 6.94734)
texture = ExtResource("25_baed2")

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
position = Vector2(145.263, 32)
texture = ExtResource("26_mp1xg")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 13.6842
offset_right = 210.737
offset_bottom = 30.6842
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 1.05264
offset_right = 210.737
offset_bottom = 18.0526
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 126.737
offset_top = 26.1053
offset_right = 210.737
offset_bottom = 43.1053
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_q2at4")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_right = 178.0
offset_bottom = 19.0
scale = Vector2(0.73, 0.73)
text = "FURNACE4_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker" instance=ExtResource("3_3na7a")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "FURNACE4_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")

[node name="Close" type="Sprite2D" parent="Control/Panel"]
position = Vector2(101.263, -125.053)
texture = ExtResource("6_0qcxk")

[node name="CloseButton" type="Button" parent="Control/Panel"]
offset_left = 91.1053
offset_top = -137.474
offset_right = 111.105
offset_bottom = -115.474
theme_override_styles/focus = SubResource("StyleBoxEmpty_pd31f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_bkysm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q2at4")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_rdlrw")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_agrao")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_vf62o")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o3kbu")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_cu0la")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_rk4c3")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_il0uu")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ii6d2")
