using Godot;

public partial class TextureManager : Node
{
	// Singleton instance
	public static TextureManager Instance { get; private set; }

	// Resource textures (for UI, inventory, etc.) - set these in the editor
	[Export] public Texture2D WoodTexture { get; set; }
	[Export] public Texture2D StoneTexture { get; set; }
	[Export] public Texture2D NetTexture { get; set; }
	[Export] public Texture2D PlankTexture { get; set; }
	[Export] public Texture2D Stone2Texture { get; set; }
	[Export] public Texture2D BerryTexture { get; set; }
	[Export] public Texture2D LeafTexture { get; set; }
	[Export] public Texture2D BranchTexture { get; set; }
	[Export] public Texture2D CharcoalTexture { get; set; }

	[Export] public Texture2D CopperOreTexture { get; set; }
	[Export] public Texture2D IronOreTexture { get; set; }
	[Export] public Texture2D GoldOreTexture { get; set; }
	[Export] public Texture2D IndigosiumOreTexture { get; set; }
	[Export] public Texture2D MithrilOreTexture { get; set; }
	[Export] public Texture2D ErithrydiumOreTexture { get; set; }
	[Export] public Texture2D AdamantiteOreTexture { get; set; }
	[Export] public Texture2D UraniumOreTexture { get; set; }

	[Export] public Texture2D CopperBarTexture { get; set; }
	[Export] public Texture2D IronBarTexture { get; set; }
	[Export] public Texture2D GoldBarTexture { get; set; }
	[Export] public Texture2D IndigosiumBarTexture { get; set; }
	[Export] public Texture2D MithrilBarTexture { get; set; }
	[Export] public Texture2D ErithrydiumBarTexture { get; set; }
	[Export] public Texture2D AdamantiteBarTexture { get; set; }
	[Export] public Texture2D UraniumBarTexture { get; set; }

	[Export] public Texture2D CopperSheetTexture { get; set; }
	[Export] public Texture2D IronSheetTexture { get; set; }
	[Export] public Texture2D GoldSheetTexture { get; set; }
	[Export] public Texture2D IndigosiumSheetTexture { get; set; }
	[Export] public Texture2D MithrilSheetTexture { get; set; }
	[Export] public Texture2D ErithrydiumSheetTexture { get; set; }
	[Export] public Texture2D AdamantiteSheetTexture { get; set; }
	[Export] public Texture2D UraniumSheetTexture { get; set; }

	[Export] public Texture2D WoodenBeamTexture { get; set; }
	[Export] public Texture2D WoodenStickTexture { get; set; }
	[Export] public Texture2D RawRabbitLegTexture { get; set; }
	[Export] public Texture2D CookedRabbitLegTexture { get; set; }
	[Export] public Texture2D WoodenKeyTexture { get; set; }
	[Export] public Texture2D StoneBrickTexture { get; set; }
	[Export] public Texture2D Stone2BrickTexture { get; set; }
	[Export] public Texture2D NailsTexture { get; set; }
	[Export] public Texture2D ArrowTexture { get; set; }
	[Export] public Texture2D CopperKeyTexture { get; set; }
	[Export] public Texture2D IronKeyTexture { get; set; }
	[Export] public Texture2D GoldKeyTexture { get; set; }
	[Export] public Texture2D IndigosiumKeyTexture { get; set; }
	[Export] public Texture2D MithrilKeyTexture { get; set; }
	[Export] public Texture2D ErithrydiumKeyTexture { get; set; }
	[Export] public Texture2D AdamantiteKeyTexture { get; set; }
	[Export] public Texture2D UraniumKeyTexture { get; set; }

	[Export] public Texture2D BrownMushroomTexture { get; set; }
	[Export] public Texture2D BlueMushroomTexture { get; set; }
	[Export] public Texture2D RedMushroomTexture { get; set; }
	[Export] public Texture2D VioletMushroomTexture { get; set; }

	// Plant textures
	[Export] public Texture2D CarrotTexture { get; set; }
	[Export] public Texture2D TurnipTexture { get; set; }
	[Export] public Texture2D PumpkinTexture { get; set; }
	[Export] public Texture2D PotatoTexture { get; set; }
	[Export] public Texture2D OnionTexture { get; set; }
	[Export] public Texture2D StrawberryTexture { get; set; }
	[Export] public Texture2D CauliflowerTexture { get; set; }
	[Export] public Texture2D TomatoTexture { get; set; }
	[Export] public Texture2D ParsnipTexture { get; set; }
	[Export] public Texture2D SnapPeasTexture { get; set; }
	[Export] public Texture2D GarlicTexture { get; set; }
	[Export] public Texture2D RadishTexture { get; set; }
	[Export] public Texture2D CornTexture { get; set; }
	[Export] public Texture2D LeekTexture { get; set; }
	[Export] public Texture2D WheatTexture { get; set; }
	[Export] public Texture2D SunflowerTexture { get; set; }
	[Export] public Texture2D BeetrootTexture { get; set; }
	[Export] public Texture2D CabbageTexture { get; set; }
	[Export] public Texture2D RedCabbageTexture { get; set; }
	[Export] public Texture2D BroccoliTexture { get; set; }
	[Export] public Texture2D BrusselsSproutTexture { get; set; }
	[Export] public Texture2D RedBellPepperTexture { get; set; }
	[Export] public Texture2D SpinachTexture { get; set; }
	[Export] public Texture2D BokChoyTexture { get; set; }
	[Export] public Texture2D ArtichokeTexture { get; set; }
	[Export] public Texture2D CottonTexture { get; set; }
	[Export] public Texture2D PurpleGrapesTexture { get; set; }
	[Export] public Texture2D GreenGrapesTexture { get; set; }
	[Export] public Texture2D RedGrapesTexture { get; set; }
	[Export] public Texture2D PinkGrapesTexture { get; set; }
	[Export] public Texture2D CantaloupeTexture { get; set; }
	[Export] public Texture2D HoneydewTexture { get; set; }
	[Export] public Texture2D ButternutSquashTexture { get; set; }
	[Export] public Texture2D BuckwheatTexture { get; set; }
	[Export] public Texture2D YellowBellPepperTexture { get; set; }
	[Export] public Texture2D OrangeBellPepperTexture { get; set; }
	[Export] public Texture2D PurpleBellPepperTexture { get; set; }
	[Export] public Texture2D WhiteBellPepperTexture { get; set; }
	[Export] public Texture2D CoffeeTexture { get; set; }
	[Export] public Texture2D AmaranthTexture { get; set; }
	[Export] public Texture2D GlassGemCornTexture { get; set; }
	[Export] public Texture2D GreenChilliPepperTexture { get; set; }
	[Export] public Texture2D RedChilliPepperTexture { get; set; }
	[Export] public Texture2D YellowChilliPepperTexture { get; set; }
	[Export] public Texture2D OrangeChilliPepperTexture { get; set; }
	[Export] public Texture2D PurpleChilliPepperTexture { get; set; }

	// Seed bag textures
	[Export] public Texture2D CarrotSeedBagTexture { get; set; }
	[Export] public Texture2D TurnipSeedBagTexture { get; set; }
	[Export] public Texture2D PumpkinSeedBagTexture { get; set; }
	[Export] public Texture2D PotatoSeedBagTexture { get; set; }
	[Export] public Texture2D OnionSeedBagTexture { get; set; }
	[Export] public Texture2D StrawberrySeedBagTexture { get; set; }
	[Export] public Texture2D CauliflowerSeedBagTexture { get; set; }
	[Export] public Texture2D TomatoSeedBagTexture { get; set; }
	[Export] public Texture2D ParsnipSeedBagTexture { get; set; }
	[Export] public Texture2D SnapPeasSeedBagTexture { get; set; }
	[Export] public Texture2D GarlicSeedBagTexture { get; set; }
	[Export] public Texture2D RadishSeedBagTexture { get; set; }
	[Export] public Texture2D CornSeedBagTexture { get; set; }
	[Export] public Texture2D LeekSeedBagTexture { get; set; }
	[Export] public Texture2D WheatSeedBagTexture { get; set; }
	[Export] public Texture2D SunflowerSeedBagTexture { get; set; }
	[Export] public Texture2D BeetrootSeedBagTexture { get; set; }
	[Export] public Texture2D CabbageSeedBagTexture { get; set; }
	[Export] public Texture2D RedCabbageSeedBagTexture { get; set; }
	[Export] public Texture2D BroccoliSeedBagTexture { get; set; }
	[Export] public Texture2D BrusselsSproutSeedBagTexture { get; set; }
	[Export] public Texture2D RedBellPepperSeedBagTexture { get; set; }
	[Export] public Texture2D SpinachSeedBagTexture { get; set; }
	[Export] public Texture2D BokChoySeedBagTexture { get; set; }
	[Export] public Texture2D ArtichokeSeedBagTexture { get; set; }
	[Export] public Texture2D CottonSeedBagTexture { get; set; }
	[Export] public Texture2D PurpleGrapesSeedBagTexture { get; set; }
	[Export] public Texture2D GreenGrapesSeedBagTexture { get; set; }
	[Export] public Texture2D RedGrapesSeedBagTexture { get; set; }
	[Export] public Texture2D PinkGrapesSeedBagTexture { get; set; }
	[Export] public Texture2D CantaloupeSeedBagTexture { get; set; }
	[Export] public Texture2D HoneydewSeedBagTexture { get; set; }
	[Export] public Texture2D ButternutSquashSeedBagTexture { get; set; }
	[Export] public Texture2D BuckwheatSeedBagTexture { get; set; }
	[Export] public Texture2D YellowBellPepperSeedBagTexture { get; set; }
	[Export] public Texture2D OrangeBellPepperSeedBagTexture { get; set; }
	[Export] public Texture2D PurpleBellPepperSeedBagTexture { get; set; }
	[Export] public Texture2D WhiteBellPepperSeedBagTexture { get; set; }
	[Export] public Texture2D CoffeeSeedBagTexture { get; set; }
	[Export] public Texture2D AmaranthSeedBagTexture { get; set; }
	[Export] public Texture2D GlassGemCornSeedBagTexture { get; set; }
	[Export] public Texture2D GreenChilliPepperSeedBagTexture { get; set; }
	[Export] public Texture2D RedChilliPepperSeedBagTexture { get; set; }
	[Export] public Texture2D YellowChilliPepperSeedBagTexture { get; set; }
	[Export] public Texture2D OrangeChilliPepperSeedBagTexture { get; set; }
	[Export] public Texture2D PurpleChilliPepperSeedBagTexture { get; set; }

	// Resource icon textures (for dropped items, small displays) - set these in the editor
	[Export] public Texture2D WoodIconTexture { get; set; }
	[Export] public Texture2D StoneIconTexture { get; set; }
	[Export] public Texture2D NetIconTexture { get; set; }
	[Export] public Texture2D PlankIconTexture { get; set; }
	[Export] public Texture2D Stone2IconTexture { get; set; }
	[Export] public Texture2D BerryIconTexture { get; set; }
	[Export] public Texture2D LeafIconTexture { get; set; }
	[Export] public Texture2D BranchIconTexture { get; set; }
	[Export] public Texture2D CharcoalIconTexture { get; set; }

	[Export] public Texture2D BrownMushroomIconTexture { get; set; }
	[Export] public Texture2D BlueMushroomIconTexture { get; set; }
	[Export] public Texture2D RedMushroomIconTexture { get; set; }
	[Export] public Texture2D VioletMushroomIconTexture { get; set; }

	// Plant icon textures
	[Export] public Texture2D CarrotIconTexture { get; set; }
	[Export] public Texture2D TurnipIconTexture { get; set; }
	[Export] public Texture2D PumpkinIconTexture { get; set; }
	[Export] public Texture2D PotatoIconTexture { get; set; }
	[Export] public Texture2D OnionIconTexture { get; set; }
	[Export] public Texture2D StrawberryIconTexture { get; set; }
	[Export] public Texture2D CauliflowerIconTexture { get; set; }
	[Export] public Texture2D TomatoIconTexture { get; set; }
	[Export] public Texture2D ParsnipIconTexture { get; set; }
	[Export] public Texture2D SnapPeasIconTexture { get; set; }
	[Export] public Texture2D GarlicIconTexture { get; set; }
	[Export] public Texture2D RadishIconTexture { get; set; }
	[Export] public Texture2D CornIconTexture { get; set; }
	[Export] public Texture2D LeekIconTexture { get; set; }
	[Export] public Texture2D WheatIconTexture { get; set; }
	[Export] public Texture2D SunflowerIconTexture { get; set; }
	[Export] public Texture2D BeetrootIconTexture { get; set; }
	[Export] public Texture2D CabbageIconTexture { get; set; }
	[Export] public Texture2D RedCabbageIconTexture { get; set; }
	[Export] public Texture2D BroccoliIconTexture { get; set; }
	[Export] public Texture2D BrusselsSproutIconTexture { get; set; }
	[Export] public Texture2D RedBellPepperIconTexture { get; set; }
	[Export] public Texture2D SpinachIconTexture { get; set; }
	[Export] public Texture2D BokChoyIconTexture { get; set; }
	[Export] public Texture2D ArtichokeIconTexture { get; set; }
	[Export] public Texture2D CottonIconTexture { get; set; }
	[Export] public Texture2D PurpleGrapesIconTexture { get; set; }
	[Export] public Texture2D GreenGrapesIconTexture { get; set; }
	[Export] public Texture2D RedGrapesIconTexture { get; set; }
	[Export] public Texture2D PinkGrapesIconTexture { get; set; }
	[Export] public Texture2D CantaloupeIconTexture { get; set; }
	[Export] public Texture2D HoneydewIconTexture { get; set; }
	[Export] public Texture2D ButternutSquashIconTexture { get; set; }
	[Export] public Texture2D BuckwheatIconTexture { get; set; }
	[Export] public Texture2D YellowBellPepperIconTexture { get; set; }
	[Export] public Texture2D OrangeBellPepperIconTexture { get; set; }
	[Export] public Texture2D PurpleBellPepperIconTexture { get; set; }
	[Export] public Texture2D WhiteBellPepperIconTexture { get; set; }
	[Export] public Texture2D CoffeeIconTexture { get; set; }
	[Export] public Texture2D AmaranthIconTexture { get; set; }
	[Export] public Texture2D GlassGemCornIconTexture { get; set; }
	[Export] public Texture2D GreenChilliPepperIconTexture { get; set; }
	[Export] public Texture2D RedChilliPepperIconTexture { get; set; }
	[Export] public Texture2D YellowChilliPepperIconTexture { get; set; }
	[Export] public Texture2D OrangeChilliPepperIconTexture { get; set; }
	[Export] public Texture2D PurpleChilliPepperIconTexture { get; set; }

	// Seed bag icon textures
	[Export] public Texture2D CarrotSeedBagIconTexture { get; set; }
	[Export] public Texture2D TurnipSeedBagIconTexture { get; set; }
	[Export] public Texture2D PumpkinSeedBagIconTexture { get; set; }
	[Export] public Texture2D PotatoSeedBagIconTexture { get; set; }
	[Export] public Texture2D OnionSeedBagIconTexture { get; set; }
	[Export] public Texture2D StrawberrySeedBagIconTexture { get; set; }
	[Export] public Texture2D CauliflowerSeedBagIconTexture { get; set; }
	[Export] public Texture2D TomatoSeedBagIconTexture { get; set; }
	[Export] public Texture2D ParsnipSeedBagIconTexture { get; set; }
	[Export] public Texture2D SnapPeasSeedBagIconTexture { get; set; }
	[Export] public Texture2D GarlicSeedBagIconTexture { get; set; }
	[Export] public Texture2D RadishSeedBagIconTexture { get; set; }
	[Export] public Texture2D CornSeedBagIconTexture { get; set; }
	[Export] public Texture2D LeekSeedBagIconTexture { get; set; }
	[Export] public Texture2D WheatSeedBagIconTexture { get; set; }
	[Export] public Texture2D SunflowerSeedBagIconTexture { get; set; }
	[Export] public Texture2D BeetrootSeedBagIconTexture { get; set; }
	[Export] public Texture2D CabbageSeedBagIconTexture { get; set; }
	[Export] public Texture2D RedCabbageSeedBagIconTexture { get; set; }
	[Export] public Texture2D BroccoliSeedBagIconTexture { get; set; }
	[Export] public Texture2D BrusselsSproutSeedBagIconTexture { get; set; }
	[Export] public Texture2D RedBellPepperSeedBagIconTexture { get; set; }
	[Export] public Texture2D SpinachSeedBagIconTexture { get; set; }
	[Export] public Texture2D BokChoySeedBagIconTexture { get; set; }
	[Export] public Texture2D ArtichokeSeedBagIconTexture { get; set; }
	[Export] public Texture2D CottonSeedBagIconTexture { get; set; }
	[Export] public Texture2D PurpleGrapesSeedBagIconTexture { get; set; }
	[Export] public Texture2D GreenGrapesSeedBagIconTexture { get; set; }
	[Export] public Texture2D RedGrapesSeedBagIconTexture { get; set; }
	[Export] public Texture2D PinkGrapesSeedBagIconTexture { get; set; }
	[Export] public Texture2D CantaloupeSeedBagIconTexture { get; set; }
	[Export] public Texture2D HoneydewSeedBagIconTexture { get; set; }
	[Export] public Texture2D ButternutSquashSeedBagIconTexture { get; set; }
	[Export] public Texture2D BuckwheatSeedBagIconTexture { get; set; }
	[Export] public Texture2D YellowBellPepperSeedBagIconTexture { get; set; }
	[Export] public Texture2D OrangeBellPepperSeedBagIconTexture { get; set; }
	[Export] public Texture2D PurpleBellPepperSeedBagIconTexture { get; set; }
	[Export] public Texture2D WhiteBellPepperSeedBagIconTexture { get; set; }
	[Export] public Texture2D CoffeeSeedBagIconTexture { get; set; }
	[Export] public Texture2D AmaranthSeedBagIconTexture { get; set; }
	[Export] public Texture2D GlassGemCornSeedBagIconTexture { get; set; }
	[Export] public Texture2D GreenChilliPepperSeedBagIconTexture { get; set; }
	[Export] public Texture2D RedChilliPepperSeedBagIconTexture { get; set; }
	[Export] public Texture2D YellowChilliPepperSeedBagIconTexture { get; set; }
	[Export] public Texture2D OrangeChilliPepperSeedBagIconTexture { get; set; }
	[Export] public Texture2D PurpleChilliPepperSeedBagIconTexture { get; set; }

	[Export] public Texture2D CopperOreIconTexture { get; set; }
	[Export] public Texture2D IronOreIconTexture { get; set; }
	[Export] public Texture2D GoldOreIconTexture { get; set; }
	[Export] public Texture2D IndigosiumOreIconTexture { get; set; }
	[Export] public Texture2D MithrilOreIconTexture { get; set; }
	[Export] public Texture2D ErithrydiumOreIconTexture { get; set; }
	[Export] public Texture2D AdamantiteOreIconTexture { get; set; }
	[Export] public Texture2D UraniumOreIconTexture { get; set; }

	[Export] public Texture2D CopperBarIconTexture { get; set; }
	[Export] public Texture2D IronBarIconTexture { get; set; }
	[Export] public Texture2D GoldBarIconTexture { get; set; }
	[Export] public Texture2D IndigosiumBarIconTexture { get; set; }
	[Export] public Texture2D MithrilBarIconTexture { get; set; }
	[Export] public Texture2D ErithrydiumBarIconTexture { get; set; }
	[Export] public Texture2D AdamantiteBarIconTexture { get; set; }
	[Export] public Texture2D UraniumBarIconTexture { get; set; }

	[Export] public Texture2D CopperSheetIconTexture { get; set; }
	[Export] public Texture2D IronSheetIconTexture { get; set; }
	[Export] public Texture2D GoldSheetIconTexture { get; set; }
	[Export] public Texture2D IndigosiumSheetIconTexture { get; set; }
	[Export] public Texture2D MithrilSheetIconTexture { get; set; }
	[Export] public Texture2D ErithrydiumSheetIconTexture { get; set; }
	[Export] public Texture2D AdamantiteSheetIconTexture { get; set; }
	[Export] public Texture2D UraniumSheetIconTexture { get; set; }

	[Export] public Texture2D WoodenBeamIconTexture { get; set; }
	[Export] public Texture2D WoodenStickIconTexture { get; set; }
	[Export] public Texture2D RawRabbitLegIconTexture { get; set; }
	[Export] public Texture2D CookedRabbitLegIconTexture { get; set; }
	[Export] public Texture2D WoodenKeyIconTexture { get; set; }
	[Export] public Texture2D StoneBrickIconTexture { get; set; }
	[Export] public Texture2D Stone2BrickIconTexture { get; set; }
	[Export] public Texture2D NailsIconTexture { get; set; }
	[Export] public Texture2D ArrowIconTexture { get; set; }
	[Export] public Texture2D CopperKeyIconTexture { get; set; }
	[Export] public Texture2D IronKeyIconTexture { get; set; }
	[Export] public Texture2D GoldKeyIconTexture { get; set; }
	[Export] public Texture2D IndigosiumKeyIconTexture { get; set; }
	[Export] public Texture2D MithrilKeyIconTexture { get; set; }
	[Export] public Texture2D ErithrydiumKeyIconTexture { get; set; }
	[Export] public Texture2D AdamantiteKeyIconTexture { get; set; }
	[Export] public Texture2D UraniumKeyIconTexture { get; set; }
	[Export] public Texture2D CoinIconTexture { get; set; }

	// Tool textures (for UI panels) - set these in the editor
	// Pickaxe variants
	[Export] public Texture2D PickaxeTexture { get; set; } // Default wooden pickaxe
	[Export] public Texture2D StonePickaxeTexture { get; set; }
	[Export] public Texture2D CopperPickaxeTexture { get; set; }
	[Export] public Texture2D IronPickaxeTexture { get; set; }
	[Export] public Texture2D GoldPickaxeTexture { get; set; }
	[Export] public Texture2D IndigosiumPickaxeTexture { get; set; }
	[Export] public Texture2D MithrilPickaxeTexture { get; set; }
	[Export] public Texture2D ErithrydiumPickaxeTexture { get; set; }
	[Export] public Texture2D AdamantitePickaxeTexture { get; set; }
	[Export] public Texture2D UraniumPickaxeTexture { get; set; }
	
	// Hammer variants
	[Export] public Texture2D HammerTexture { get; set; } // Default wooden hammer
	[Export] public Texture2D StoneHammerTexture { get; set; }
	[Export] public Texture2D CopperHammerTexture { get; set; }
	[Export] public Texture2D IronHammerTexture { get; set; }
	[Export] public Texture2D GoldHammerTexture { get; set; }
	[Export] public Texture2D IndigosiumHammerTexture { get; set; }
	[Export] public Texture2D MithrilHammerTexture { get; set; }
	[Export] public Texture2D ErithrydiumHammerTexture { get; set; }
	[Export] public Texture2D AdamantiteHammerTexture { get; set; }
	[Export] public Texture2D UraniumHammerTexture { get; set; }
	
	// Other tools
	[Export] public Texture2D WateringCanTexture { get; set; }
	[Export] public Texture2D HoeTexture { get; set; }
	[Export] public Texture2D SwordTexture { get; set; }
	[Export] public Texture2D BowTexture { get; set; }

	public override void _Ready()
	{
		// Set singleton instance
		if (Instance == null)
		{
			Instance = this;
			GD.Print("TextureManager initialized and ready");
		}
		else
		{
			// Prevent duplicate instances
			QueueFree();
		}
	}

	public Texture2D GetResourceTexture(ResourceType resourceType)
	{
		return resourceType switch
		{
			ResourceType.Wood => WoodTexture,
			ResourceType.Stone => StoneTexture,
			ResourceType.Net => NetTexture,
			ResourceType.Plank => PlankTexture,
			ResourceType.Stone2 => Stone2Texture,
			ResourceType.Berry => BerryTexture,
			ResourceType.Leaf => LeafTexture,
			ResourceType.Branch => BranchTexture,
			ResourceType.Charcoal => CharcoalTexture,
			ResourceType.CopperOre => CopperOreTexture,
			ResourceType.IronOre => IronOreTexture,
			ResourceType.GoldOre => GoldOreTexture,
			ResourceType.IndigosiumOre => IndigosiumOreTexture,
			ResourceType.MithrilOre => MithrilOreTexture,
			ResourceType.ErithrydiumOre => ErithrydiumOreTexture,
			ResourceType.AdamantiteOre => AdamantiteOreTexture,
			ResourceType.UraniumOre => UraniumOreTexture,
			ResourceType.CopperBar => CopperBarTexture,
			ResourceType.IronBar => IronBarTexture,
			ResourceType.GoldBar => GoldBarTexture,
			ResourceType.IndigosiumBar => IndigosiumBarTexture,
			ResourceType.MithrilBar => MithrilBarTexture,
			ResourceType.ErithrydiumBar => ErithrydiumBarTexture,
			ResourceType.AdamantiteBar => AdamantiteBarTexture,
			ResourceType.UraniumBar => UraniumBarTexture,
			ResourceType.CopperSheet => CopperSheetTexture,
			ResourceType.IronSheet => IronSheetTexture,
			ResourceType.GoldSheet => GoldSheetTexture,
			ResourceType.IndigosiumSheet => IndigosiumSheetTexture,
			ResourceType.MithrilSheet => MithrilSheetTexture,
			ResourceType.ErithrydiumSheet => ErithrydiumSheetTexture,
			ResourceType.AdamantiteSheet => AdamantiteSheetTexture,
			ResourceType.UraniumSheet => UraniumSheetTexture,
			ResourceType.WoodenBeam => WoodenBeamTexture,
			ResourceType.WoodenStick => WoodenStickTexture,
			ResourceType.RawRabbitLeg => RawRabbitLegTexture,
			ResourceType.CookedRabbitLeg => CookedRabbitLegTexture,
			ResourceType.WoodenKey => WoodenKeyTexture,
			ResourceType.StoneBrick => StoneBrickTexture,
			ResourceType.Stone2Brick => Stone2BrickTexture,
			ResourceType.Nails => NailsTexture,
			ResourceType.Arrow => ArrowTexture,
			ResourceType.CopperKey => CopperKeyTexture,
			ResourceType.IronKey => IronKeyTexture,
			ResourceType.GoldKey => GoldKeyTexture,
			ResourceType.IndigosiumKey => IndigosiumKeyTexture,
			ResourceType.MithrilKey => MithrilKeyTexture,
			ResourceType.ErithrydiumKey => ErithrydiumKeyTexture,
			ResourceType.AdamantiteKey => AdamantiteKeyTexture,
			ResourceType.UraniumKey => UraniumKeyTexture,
			ResourceType.BrownMushroom => BrownMushroomTexture,
			ResourceType.BlueMushroom => BlueMushroomTexture,
			ResourceType.RedMushroom => RedMushroomTexture,
			ResourceType.VioletMushroom => VioletMushroomTexture,
			// Plants
			ResourceType.Carrot => CarrotTexture,
			ResourceType.Turnip => TurnipTexture,
			ResourceType.Pumpkin => PumpkinTexture,
			ResourceType.Potato => PotatoTexture,
			ResourceType.Onion => OnionTexture,
			ResourceType.Strawberry => StrawberryTexture,
			ResourceType.Cauliflower => CauliflowerTexture,
			ResourceType.Tomato => TomatoTexture,
			ResourceType.Parsnip => ParsnipTexture,
			ResourceType.SnapPeas => SnapPeasTexture,
			ResourceType.Garlic => GarlicTexture,
			ResourceType.Radish => RadishTexture,
			ResourceType.Corn => CornTexture,
			ResourceType.Leek => LeekTexture,
			ResourceType.Wheat => WheatTexture,
			ResourceType.Sunflower => SunflowerTexture,
			ResourceType.Beetroot => BeetrootTexture,
			ResourceType.Cabbage => CabbageTexture,
			ResourceType.RedCabbage => RedCabbageTexture,
			ResourceType.Broccoli => BroccoliTexture,
			ResourceType.BrusselsSprout => BrusselsSproutTexture,
			ResourceType.RedBellPepper => RedBellPepperTexture,
			ResourceType.Spinach => SpinachTexture,
			ResourceType.BokChoy => BokChoyTexture,
			ResourceType.Artichoke => ArtichokeTexture,
			ResourceType.Cotton => CottonTexture,
			ResourceType.PurpleGrapes => PurpleGrapesTexture,
			ResourceType.GreenGrapes => GreenGrapesTexture,
			ResourceType.RedGrapes => RedGrapesTexture,
			ResourceType.PinkGrapes => PinkGrapesTexture,
			ResourceType.Cantaloupe => CantaloupeTexture,
			ResourceType.Honeydew => HoneydewTexture,
			ResourceType.ButternutSquash => ButternutSquashTexture,
			ResourceType.Buckwheat => BuckwheatTexture,
			ResourceType.YellowBellPepper => YellowBellPepperTexture,
			ResourceType.OrangeBellPepper => OrangeBellPepperTexture,
			ResourceType.PurpleBellPepper => PurpleBellPepperTexture,
			ResourceType.WhiteBellPepper => WhiteBellPepperTexture,
			ResourceType.Coffee => CoffeeTexture,
			ResourceType.Amaranth => AmaranthTexture,
			ResourceType.GlassGemCorn => GlassGemCornTexture,
			ResourceType.GreenChilliPepper => GreenChilliPepperTexture,
			ResourceType.RedChilliPepper => RedChilliPepperTexture,
			ResourceType.YellowChilliPepper => YellowChilliPepperTexture,
			ResourceType.OrangeChilliPepper => OrangeChilliPepperTexture,
			ResourceType.PurpleChilliPepper => PurpleChilliPepperTexture,
			// Seed bags
			ResourceType.CarrotSeedBag => CarrotSeedBagTexture,
			ResourceType.TurnipSeedBag => TurnipSeedBagTexture,
			ResourceType.PumpkinSeedBag => PumpkinSeedBagTexture,
			ResourceType.PotatoSeedBag => PotatoSeedBagTexture,
			ResourceType.OnionSeedBag => OnionSeedBagTexture,
			ResourceType.StrawberrySeedBag => StrawberrySeedBagTexture,
			ResourceType.CauliflowerSeedBag => CauliflowerSeedBagTexture,
			ResourceType.TomatoSeedBag => TomatoSeedBagTexture,
			ResourceType.ParsnipSeedBag => ParsnipSeedBagTexture,
			ResourceType.SnapPeasSeedBag => SnapPeasSeedBagTexture,
			ResourceType.GarlicSeedBag => GarlicSeedBagTexture,
			ResourceType.RadishSeedBag => RadishSeedBagTexture,
			ResourceType.CornSeedBag => CornSeedBagTexture,
			ResourceType.LeekSeedBag => LeekSeedBagTexture,
			ResourceType.WheatSeedBag => WheatSeedBagTexture,
			ResourceType.SunflowerSeedBag => SunflowerSeedBagTexture,
			ResourceType.BeetrootSeedBag => BeetrootSeedBagTexture,
			ResourceType.CabbageSeedBag => CabbageSeedBagTexture,
			ResourceType.RedCabbageSeedBag => RedCabbageSeedBagTexture,
			ResourceType.BroccoliSeedBag => BroccoliSeedBagTexture,
			ResourceType.BrusselsSproutSeedBag => BrusselsSproutSeedBagTexture,
			ResourceType.RedBellPepperSeedBag => RedBellPepperSeedBagTexture,
			ResourceType.SpinachSeedBag => SpinachSeedBagTexture,
			ResourceType.BokChoySeedBag => BokChoySeedBagTexture,
			ResourceType.ArtichokeSeedBag => ArtichokeSeedBagTexture,
			ResourceType.CottonSeedBag => CottonSeedBagTexture,
			ResourceType.PurpleGrapesSeedBag => PurpleGrapesSeedBagTexture,
			ResourceType.GreenGrapesSeedBag => GreenGrapesSeedBagTexture,
			ResourceType.RedGrapesSeedBag => RedGrapesSeedBagTexture,
			ResourceType.PinkGrapesSeedBag => PinkGrapesSeedBagTexture,
			ResourceType.CantaloupeSeedBag => CantaloupeSeedBagTexture,
			ResourceType.HoneydewSeedBag => HoneydewSeedBagTexture,
			ResourceType.ButternutSquashSeedBag => ButternutSquashSeedBagTexture,
			ResourceType.BuckwheatSeedBag => BuckwheatSeedBagTexture,
			ResourceType.YellowBellPepperSeedBag => YellowBellPepperSeedBagTexture,
			ResourceType.OrangeBellPepperSeedBag => OrangeBellPepperSeedBagTexture,
			ResourceType.PurpleBellPepperSeedBag => PurpleBellPepperSeedBagTexture,
			ResourceType.WhiteBellPepperSeedBag => WhiteBellPepperSeedBagTexture,
			ResourceType.CoffeeSeedBag => CoffeeSeedBagTexture,
			ResourceType.AmaranthSeedBag => AmaranthSeedBagTexture,
			ResourceType.GlassGemCornSeedBag => GlassGemCornSeedBagTexture,
			ResourceType.GreenChilliPepperSeedBag => GreenChilliPepperSeedBagTexture,
			ResourceType.RedChilliPepperSeedBag => RedChilliPepperSeedBagTexture,
			ResourceType.YellowChilliPepperSeedBag => YellowChilliPepperSeedBagTexture,
			ResourceType.OrangeChilliPepperSeedBag => OrangeChilliPepperSeedBagTexture,
			ResourceType.PurpleChilliPepperSeedBag => PurpleChilliPepperSeedBagTexture,
			_ => null
		};
	}

	public Texture2D GetResourceIconTexture(ResourceType resourceType)
	{
		return resourceType switch
		{
			ResourceType.Wood => WoodIconTexture,
			ResourceType.Stone => StoneIconTexture,
			ResourceType.Net => NetIconTexture,
			ResourceType.Plank => PlankIconTexture,
			ResourceType.Stone2 => Stone2IconTexture,
			ResourceType.Berry => BerryIconTexture,
			ResourceType.Leaf => LeafIconTexture,
			ResourceType.Branch => BranchIconTexture,
			ResourceType.Charcoal => CharcoalIconTexture,
			ResourceType.CopperOre => CopperOreIconTexture,
			ResourceType.IronOre => IronOreIconTexture,
			ResourceType.GoldOre => GoldOreIconTexture,
			ResourceType.IndigosiumOre => IndigosiumOreIconTexture,
			ResourceType.MithrilOre => MithrilOreIconTexture,
			ResourceType.ErithrydiumOre => ErithrydiumOreIconTexture,
			ResourceType.AdamantiteOre => AdamantiteOreIconTexture,
			ResourceType.UraniumOre => UraniumOreIconTexture,
			ResourceType.CopperBar => CopperBarIconTexture,
			ResourceType.IronBar => IronBarIconTexture,
			ResourceType.GoldBar => GoldBarIconTexture,
			ResourceType.IndigosiumBar => IndigosiumBarIconTexture,
			ResourceType.MithrilBar => MithrilBarIconTexture,
			ResourceType.ErithrydiumBar => ErithrydiumBarIconTexture,
			ResourceType.AdamantiteBar => AdamantiteBarIconTexture,
			ResourceType.UraniumBar => UraniumBarIconTexture,
			ResourceType.CopperSheet => CopperSheetIconTexture,
			ResourceType.IronSheet => IronSheetIconTexture,
			ResourceType.GoldSheet => GoldSheetIconTexture,
			ResourceType.IndigosiumSheet => IndigosiumSheetIconTexture,
			ResourceType.MithrilSheet => MithrilSheetIconTexture,
			ResourceType.ErithrydiumSheet => ErithrydiumSheetIconTexture,
			ResourceType.AdamantiteSheet => AdamantiteSheetIconTexture,
			ResourceType.UraniumSheet => UraniumSheetIconTexture,
			ResourceType.WoodenBeam => WoodenBeamIconTexture,
			ResourceType.WoodenStick => WoodenStickIconTexture,
			ResourceType.RawRabbitLeg => RawRabbitLegIconTexture,
			ResourceType.CookedRabbitLeg => CookedRabbitLegIconTexture,
			ResourceType.WoodenKey => WoodenKeyIconTexture,
			ResourceType.StoneBrick => StoneBrickIconTexture,
			ResourceType.Stone2Brick => Stone2BrickIconTexture,
			ResourceType.Nails => NailsIconTexture,
			ResourceType.Arrow => ArrowIconTexture,
			ResourceType.CopperKey => CopperKeyIconTexture,
			ResourceType.IronKey => IronKeyIconTexture,
			ResourceType.GoldKey => GoldKeyIconTexture,
			ResourceType.IndigosiumKey => IndigosiumKeyIconTexture,
			ResourceType.MithrilKey => MithrilKeyIconTexture,
			ResourceType.ErithrydiumKey => ErithrydiumKeyIconTexture,
			ResourceType.AdamantiteKey => AdamantiteKeyIconTexture,
			ResourceType.UraniumKey => UraniumKeyIconTexture,
			ResourceType.BrownMushroom => BrownMushroomIconTexture,
			ResourceType.BlueMushroom => BlueMushroomIconTexture,
			ResourceType.RedMushroom => RedMushroomIconTexture,
			ResourceType.VioletMushroom => VioletMushroomIconTexture,
			// Plants
			ResourceType.Carrot => CarrotIconTexture,
			ResourceType.Turnip => TurnipIconTexture,
			ResourceType.Pumpkin => PumpkinIconTexture,
			ResourceType.Potato => PotatoIconTexture,
			ResourceType.Onion => OnionIconTexture,
			ResourceType.Strawberry => StrawberryIconTexture,
			ResourceType.Cauliflower => CauliflowerIconTexture,
			ResourceType.Tomato => TomatoIconTexture,
			ResourceType.Parsnip => ParsnipIconTexture,
			ResourceType.SnapPeas => SnapPeasIconTexture,
			ResourceType.Garlic => GarlicIconTexture,
			ResourceType.Radish => RadishIconTexture,
			ResourceType.Corn => CornIconTexture,
			ResourceType.Leek => LeekIconTexture,
			ResourceType.Wheat => WheatIconTexture,
			ResourceType.Sunflower => SunflowerIconTexture,
			ResourceType.Beetroot => BeetrootIconTexture,
			ResourceType.Cabbage => CabbageIconTexture,
			ResourceType.RedCabbage => RedCabbageIconTexture,
			ResourceType.Broccoli => BroccoliIconTexture,
			ResourceType.BrusselsSprout => BrusselsSproutIconTexture,
			ResourceType.RedBellPepper => RedBellPepperIconTexture,
			ResourceType.Spinach => SpinachIconTexture,
			ResourceType.BokChoy => BokChoyIconTexture,
			ResourceType.Artichoke => ArtichokeIconTexture,
			ResourceType.Cotton => CottonIconTexture,
			ResourceType.PurpleGrapes => PurpleGrapesIconTexture,
			ResourceType.GreenGrapes => GreenGrapesIconTexture,
			ResourceType.RedGrapes => RedGrapesIconTexture,
			ResourceType.PinkGrapes => PinkGrapesIconTexture,
			ResourceType.Cantaloupe => CantaloupeIconTexture,
			ResourceType.Honeydew => HoneydewIconTexture,
			ResourceType.ButternutSquash => ButternutSquashIconTexture,
			ResourceType.Buckwheat => BuckwheatIconTexture,
			ResourceType.YellowBellPepper => YellowBellPepperIconTexture,
			ResourceType.OrangeBellPepper => OrangeBellPepperIconTexture,
			ResourceType.PurpleBellPepper => PurpleBellPepperIconTexture,
			ResourceType.WhiteBellPepper => WhiteBellPepperIconTexture,
			ResourceType.Coffee => CoffeeIconTexture,
			ResourceType.Amaranth => AmaranthIconTexture,
			ResourceType.GlassGemCorn => GlassGemCornIconTexture,
			ResourceType.GreenChilliPepper => GreenChilliPepperIconTexture,
			ResourceType.RedChilliPepper => RedChilliPepperIconTexture,
			ResourceType.YellowChilliPepper => YellowChilliPepperIconTexture,
			ResourceType.OrangeChilliPepper => OrangeChilliPepperIconTexture,
			ResourceType.PurpleChilliPepper => PurpleChilliPepperIconTexture,
			// Seed bags
			ResourceType.CarrotSeedBag => CarrotSeedBagIconTexture,
			ResourceType.TurnipSeedBag => TurnipSeedBagIconTexture,
			ResourceType.PumpkinSeedBag => PumpkinSeedBagIconTexture,
			ResourceType.PotatoSeedBag => PotatoSeedBagIconTexture,
			ResourceType.OnionSeedBag => OnionSeedBagIconTexture,
			ResourceType.StrawberrySeedBag => StrawberrySeedBagIconTexture,
			ResourceType.CauliflowerSeedBag => CauliflowerSeedBagIconTexture,
			ResourceType.TomatoSeedBag => TomatoSeedBagIconTexture,
			ResourceType.ParsnipSeedBag => ParsnipSeedBagIconTexture,
			ResourceType.SnapPeasSeedBag => SnapPeasSeedBagIconTexture,
			ResourceType.GarlicSeedBag => GarlicSeedBagIconTexture,
			ResourceType.RadishSeedBag => RadishSeedBagIconTexture,
			ResourceType.CornSeedBag => CornSeedBagIconTexture,
			ResourceType.LeekSeedBag => LeekSeedBagIconTexture,
			ResourceType.WheatSeedBag => WheatSeedBagIconTexture,
			ResourceType.SunflowerSeedBag => SunflowerSeedBagIconTexture,
			ResourceType.BeetrootSeedBag => BeetrootSeedBagIconTexture,
			ResourceType.CabbageSeedBag => CabbageSeedBagIconTexture,
			ResourceType.RedCabbageSeedBag => RedCabbageSeedBagIconTexture,
			ResourceType.BroccoliSeedBag => BroccoliSeedBagIconTexture,
			ResourceType.BrusselsSproutSeedBag => BrusselsSproutSeedBagIconTexture,
			ResourceType.RedBellPepperSeedBag => RedBellPepperSeedBagIconTexture,
			ResourceType.SpinachSeedBag => SpinachSeedBagIconTexture,
			ResourceType.BokChoySeedBag => BokChoySeedBagIconTexture,
			ResourceType.ArtichokeSeedBag => ArtichokeSeedBagIconTexture,
			ResourceType.CottonSeedBag => CottonSeedBagIconTexture,
			ResourceType.PurpleGrapesSeedBag => PurpleGrapesSeedBagIconTexture,
			ResourceType.GreenGrapesSeedBag => GreenGrapesSeedBagIconTexture,
			ResourceType.RedGrapesSeedBag => RedGrapesSeedBagIconTexture,
			ResourceType.PinkGrapesSeedBag => PinkGrapesSeedBagIconTexture,
			ResourceType.CantaloupeSeedBag => CantaloupeSeedBagIconTexture,
			ResourceType.HoneydewSeedBag => HoneydewSeedBagIconTexture,
			ResourceType.ButternutSquashSeedBag => ButternutSquashSeedBagIconTexture,
			ResourceType.BuckwheatSeedBag => BuckwheatSeedBagIconTexture,
			ResourceType.YellowBellPepperSeedBag => YellowBellPepperSeedBagIconTexture,
			ResourceType.OrangeBellPepperSeedBag => OrangeBellPepperSeedBagIconTexture,
			ResourceType.PurpleBellPepperSeedBag => PurpleBellPepperSeedBagIconTexture,
			ResourceType.WhiteBellPepperSeedBag => WhiteBellPepperSeedBagIconTexture,
			ResourceType.CoffeeSeedBag => CoffeeSeedBagIconTexture,
			ResourceType.AmaranthSeedBag => AmaranthSeedBagIconTexture,
			ResourceType.GlassGemCornSeedBag => GlassGemCornSeedBagIconTexture,
			ResourceType.GreenChilliPepperSeedBag => GreenChilliPepperSeedBagIconTexture,
			ResourceType.RedChilliPepperSeedBag => RedChilliPepperSeedBagIconTexture,
			ResourceType.YellowChilliPepperSeedBag => YellowChilliPepperSeedBagIconTexture,
			ResourceType.OrangeChilliPepperSeedBag => OrangeChilliPepperSeedBagIconTexture,
			ResourceType.PurpleChilliPepperSeedBag => PurpleChilliPepperSeedBagIconTexture,
			_ => null
		};
	}

	public bool HasTexture(ResourceType resourceType)
	{
		return GetResourceTexture(resourceType) != null;
	}

	public bool HasIconTexture(ResourceType resourceType)
	{
		return GetResourceIconTexture(resourceType) != null;
	}

	public Texture2D GetToolTexture(ToolType toolType, int level = 1)
	{
		if (toolType == ToolType.Pickaxe)
		{
			return level switch
			{
				1 => PickaxeTexture,
				2 => StonePickaxeTexture,
				3 => CopperPickaxeTexture,
				4 => IronPickaxeTexture,
				5 => GoldPickaxeTexture,
				6 => IndigosiumPickaxeTexture,
				7 => MithrilPickaxeTexture,
				8 => ErithrydiumPickaxeTexture,
				9 => AdamantitePickaxeTexture,
				10 => UraniumPickaxeTexture,
			};
		}
		else if (toolType == ToolType.Hammer)
		{
			return level switch
			{
				1 => HammerTexture,
				2 => StoneHammerTexture,
				3 => CopperHammerTexture,
				4 => IronHammerTexture,
				5 => GoldHammerTexture,
				6 => IndigosiumHammerTexture,
				7 => MithrilHammerTexture,
				8 => ErithrydiumHammerTexture,
				9 => AdamantiteHammerTexture,
				10 => UraniumHammerTexture,
			};
		}
		else
		{
			return toolType switch
			{
				ToolType.Hoe => HoeTexture,
				ToolType.WateringCan => WateringCanTexture,
				ToolType.Sword => SwordTexture,
				ToolType.Bow => BowTexture,
				_ => null
			};
		}
	}
}
