using Godot;

public partial class SeedMakerMenu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _carrotSeedBagSelectButton;
	private Button _turnipSeedBagSelectButton;

	// InfoBoard controls
	private Sprite2D _itemFront;
	private Label _amountToProduce;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;

	private SeedMaker _currentSeedMaker;
	private ResourceType _selectedResource = ResourceType.None;
	private int _selectedAmount = 0;

	public override void _Ready()
	{
		GD.Print("SeedMakerMenu: Starting _Ready");

		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_carrotSeedBagSelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag/Button");
		_turnipSeedBagSelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListTurnipSeedBag/Button");

		// InfoBoard controls
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");

		// Connect signals
		_closeButton.Pressed += OnCloseButtonPressed;
		_carrotSeedBagSelectButton.Pressed += OnCarrotSeedBagSelectButtonPressed;
		_turnipSeedBagSelectButton.Pressed += OnTurnipSeedBagSelectButtonPressed;

		_buttonMinusOne.Pressed += OnButtonMinusOnePressed;
		_buttonPlusOne.Pressed += OnButtonPlusOnePressed;
		_buttonSetOne.Pressed += OnButtonSetOnePressed;
		_buttonSet25Percent.Pressed += OnButtonSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnButtonSet50PercentPressed;
		_buttonSetMax.Pressed += OnButtonSetMaxPressed;
		_buttonProduce.Pressed += OnButtonProducePressed;

		// Set Control/Panel to initially hidden
		var panel = GetNode<Node2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = false;
		}

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}

		UpdateInfoBoard();
		GD.Print("SeedMakerMenu: Ready completed");

		// Register with MenuManager for ESC key handling
		MenuManager.Instance?.RegisterMenu("SeedMakerMenu", this);
	}

	public bool IsMenuOpen()
	{
		var panel = GetNode<Sprite2D>("Control/Panel");
		return panel != null && panel.Visible;
	}

	public void SetSeedMaker(SeedMaker seedMaker)
	{
		_currentSeedMaker = seedMaker;
	}

	private void OnCloseButtonPressed()
	{
		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("SeedMakerMenu");
		}
		else
		{
			CloseMenu();
		}
	}

	private void OnPlankSelectButtonPressed()
	{
		_selectedResource = ResourceType.Plank;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnWoodenBeamSelectButtonPressed()
	{
		_selectedResource = ResourceType.WoodenBeam;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnButtonMinusOnePressed()
	{
		_selectedAmount = Mathf.Max(0, _selectedAmount - 1);
		UpdateInfoBoard();
	}

	private void OnButtonPlusOnePressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = Mathf.Min(maxAffordable, _selectedAmount + 1);
		UpdateInfoBoard();
	}

	private void OnButtonSetOnePressed()
	{
		_selectedAmount = Mathf.Min(1, GetMaxAffordableAmount());
		UpdateInfoBoard();
	}

	private void OnButtonSet25PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 4) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSet50PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 2) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSetMaxPressed()
	{
		_selectedAmount = GetMaxAffordableAmount();
		UpdateInfoBoard();
	}

	private void OnButtonProducePressed()
	{
		if (_selectedResource == ResourceType.None || _selectedAmount <= 0 || _currentSeedMaker == null)
			return;

		// Verify player has enough resources before starting production
		int maxAffordable = GetMaxAffordableAmount();
		if (maxAffordable < _selectedAmount)
		{
			// Not enough resources, set amount to 0 and update display
			_selectedAmount = 0;
			UpdateInfoBoard();
			return;
		}

		_currentSeedMaker.StartCrafting(_selectedResource, _selectedAmount);

		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("SeedMakerMenu");
		}
		else
		{
			CloseMenu();
		}
	}

	private void UpdateInfoBoard()
	{
		if (_itemFront == null || _amountToProduce == null) return;

		if (_selectedResource != ResourceType.None)
		{
			var texture = TextureManager.Instance?.GetResourceTexture(_selectedResource);
			_itemFront.Texture = texture;
			_itemFront.Visible = true;
		}
		else
		{
			_itemFront.Visible = false;
		}

		_amountToProduce.Text = _selectedAmount.ToString();
	}

	private int GetMaxAffordableAmount()
	{
		if (_selectedResource == ResourceType.None) return 0;

		int maxAmount = 0;

		switch (_selectedResource)
		{
			case ResourceType.Plank:
				int wood = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Wood);
				maxAmount = wood / 2;
				break;
			case ResourceType.WoodenBeam:
				int planks = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Plank);
				maxAmount = planks / 3;
				break;
		}

		return Mathf.Max(0, maxAmount);
	}

	public void OpenMenu(SeedMaker seedMaker)
	{
		_currentSeedMaker = seedMaker;

		// Disable player movement when menu opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		// Make sure panel is visible before playing animation
		var panel = GetNode<Node2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = true;
		}

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}

		UpdateInfoBoard();
		GD.Print("SeedMakerMenu: Menu opened");
	}

	public void CloseMenu()
	{
		_currentSeedMaker = null;

		// Re-enable player movement when menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
			// Connect to animation finished to hide panel after close animation
			if (!_animationPlayer.IsConnected(AnimationPlayer.SignalName.AnimationFinished, Callable.From<StringName>(OnCloseAnimationFinished)))
			{
				_animationPlayer.AnimationFinished += OnCloseAnimationFinished;
			}
		}
		else
		{
			// Fallback if no animation player
			var panel = GetNode<Node2D>("Control/Panel");
			if (panel != null)
			{
				panel.Visible = false;
			}
		}

		GD.Print("SeedMakerMenu: Menu closed");
	}

	private void OnCloseAnimationFinished(StringName animName)
	{
		if (animName == "Close")
		{
			var panel = GetNode<Node2D>("Control/Panel");
			if (panel != null)
			{
				panel.Visible = false;
			}
			_animationPlayer.AnimationFinished -= OnCloseAnimationFinished;
		}
	}

	// IMenu interface implementation
	void IMenu.OpenMenu()
	{
		// Default implementation - requires seed maker to be set externally
		if (_currentSeedMaker != null)
		{
			OpenMenu(_currentSeedMaker);
		}
	}

}
