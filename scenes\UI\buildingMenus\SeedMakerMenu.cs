using Godot;
using System;
using System.Collections.Generic;

public partial class SeedMakerMenu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;

	// InfoBoard controls
	private Sprite2D _itemFront;
	private Label _amountToProduce;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;

	// Progress tracking
	private Label _unlockProgressLabel;
	private ItemList _nextUnlockInfoItemList;
	private Label _nextUnlockInfoLabel;

	// All seed bag buttons and ItemLists - will be shown/hidden based on unlock status
	private Dictionary<ResourceType, Button> _seedBagButtons = new();
	private Dictionary<ResourceType, ItemList> _seedBagItemLists = new();

	private SeedMaker _currentSeedMaker;
	private ResourceType _selectedResource = ResourceType.None;
	private int _selectedAmount = 0;

	public override void _Ready()
	{
		GD.Print("SeedMakerMenu: Starting _Ready");

		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");

		InitializeExistingSeedBagButtons();

		// InfoBoard controls
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");
		_unlockProgressLabel = GetNode<Label>("Control/Panel/UnlockProgressLabel2");

		// Connect signals
		_closeButton.Pressed += OnCloseButtonPressed;

		_buttonMinusOne.Pressed += OnButtonMinusOnePressed;
		_buttonPlusOne.Pressed += OnButtonPlusOnePressed;
		_buttonSetOne.Pressed += OnButtonSetOnePressed;
		_buttonSet25Percent.Pressed += OnButtonSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnButtonSet50PercentPressed;
		_buttonSetMax.Pressed += OnButtonSetMaxPressed;
		_buttonProduce.Pressed += OnButtonProducePressed;

		// Set Control/Panel to initially hidden
		var panel = GetNode<Node2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = false;
		}

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}

		UpdateInfoBoard();
		GD.Print("SeedMakerMenu: Ready completed");

		// Register with MenuManager for ESC key handling
		MenuManager.Instance?.RegisterMenu("SeedMakerMenu", this);
	}

	public bool IsMenuOpen()
	{
		var panel = GetNode<Sprite2D>("Control/Panel");
		return panel != null && panel.Visible;
	}

	public void SetSeedMaker(SeedMaker seedMaker)
	{
		_currentSeedMaker = seedMaker;
	}

	private void OnCloseButtonPressed()
	{
		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("SeedMakerMenu");
		}
		else
		{
			CloseMenu();
		}
	}

	private void InitializeExistingSeedBagButtons()
	{
		// Get the VBoxContainer where we'll add all seed bag ItemLists
		var vboxContainer = GetNode<VBoxContainer>("Control/Panel/ScrollContainer/VBoxContainer");

		// Initialize the existing CarrotSeedBag from .tscn file
		try
		{
			var carrotItemList = GetNode<ItemList>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag");
			var carrotButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag/Button");

			if (carrotItemList != null && carrotButton != null)
			{
				_seedBagItemLists[ResourceType.CarrotSeedBag] = carrotItemList;
				_seedBagButtons[ResourceType.CarrotSeedBag] = carrotButton;
				carrotButton.Pressed += () => OnSeedBagSelected(ResourceType.CarrotSeedBag);
				GD.Print("SeedMakerMenu: Initialized CarrotSeedBag button from .tscn");
			}
		}
		catch (System.Exception e)
		{
			GD.PrintErr($"SeedMakerMenu: Failed to initialize CarrotSeedBag: {e.Message}");
		}

		// Create all other seed bag ItemLists dynamically
		CreateDynamicSeedBagItemLists(vboxContainer);
	}

	private void CreateDynamicSeedBagItemLists(VBoxContainer vboxContainer)
	{
		// All seed bags except CarrotSeedBag (which is in .tscn file)
		var seedBagTypes = new ResourceType[]
		{
			ResourceType.TurnipSeedBag, ResourceType.PumpkinSeedBag, ResourceType.PotatoSeedBag,
			ResourceType.OnionSeedBag, ResourceType.StrawberrySeedBag, ResourceType.CauliflowerSeedBag, ResourceType.TomatoSeedBag,
			ResourceType.ParsnipSeedBag, ResourceType.SnapPeasSeedBag, ResourceType.GarlicSeedBag, ResourceType.RadishSeedBag,
			ResourceType.CornSeedBag, ResourceType.LeekSeedBag, ResourceType.WheatSeedBag, ResourceType.SunflowerSeedBag,
			ResourceType.BeetrootSeedBag, ResourceType.CabbageSeedBag, ResourceType.RedCabbageSeedBag, ResourceType.BroccoliSeedBag,
			ResourceType.BrusselsSproutSeedBag, ResourceType.RedBellPepperSeedBag, ResourceType.SpinachSeedBag, ResourceType.BokChoySeedBag,
			ResourceType.ArtichokeSeedBag, ResourceType.CottonSeedBag, ResourceType.PurpleGrapesSeedBag, ResourceType.GreenGrapesSeedBag,
			ResourceType.RedGrapesSeedBag, ResourceType.PinkGrapesSeedBag, ResourceType.CantaloupeSeedBag, ResourceType.HoneydewSeedBag,
			ResourceType.ButternutSquashSeedBag, ResourceType.BuckwheatSeedBag, ResourceType.YellowBellPepperSeedBag, ResourceType.OrangeBellPepperSeedBag,
			ResourceType.PurpleBellPepperSeedBag, ResourceType.WhiteBellPepperSeedBag, ResourceType.CoffeeSeedBag, ResourceType.AmaranthSeedBag,
			ResourceType.GlassGemCornSeedBag, ResourceType.GreenChilliPepperSeedBag, ResourceType.RedChilliPepperSeedBag, ResourceType.YellowChilliPepperSeedBag,
			ResourceType.OrangeChilliPepperSeedBag, ResourceType.PurpleChilliPepperSeedBag
		};

		// Get the CarrotSeedBag position to insert after it
		var carrotItemList = GetNode<ItemList>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag");
		int carrotIndex = carrotItemList.GetIndex();

		// Create all other seed bag ItemLists and insert them after CarrotSeedBag
		for (int i = 0; i < seedBagTypes.Length; i++)
		{
			var seedBagType = seedBagTypes[i];
			CreateSeedBagItemList(vboxContainer, seedBagType, carrotIndex + 1 + i);
		}

		// Create the NextUnlockInfo ItemList at the very end
		CreateNextUnlockInfoItemList(vboxContainer);
	}

	private void CreateSeedBagItemList(VBoxContainer vboxContainer, ResourceType seedBagType, int insertIndex)
	{
		// Get the template ItemList to duplicate
		var templateItemList = GetNode<ItemList>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag");

		// Duplicate the entire ItemList
		var itemList = (ItemList)templateItemList.Duplicate();
		itemList.Name = $"ItemList{seedBagType}";

		// Update the IconForeground with the correct seed bag texture
		var iconForeground = itemList.GetNode<Sprite2D>("IconForeground");
		if (iconForeground != null)
		{
			iconForeground.Texture = TextureManager.Instance.GetResourceTexture(seedBagType);
		}

		// Update the item name and description labels
		var itemName = itemList.GetNode<Label>("ItemName");
		if (itemName != null)
		{
			itemName.Text = Tr($"{seedBagType.ToString().ToUpper()}_SEED_BAG_TEXT");
		}

		var itemDescription = itemList.GetNode<Label>("ItemDescription");
		if (itemDescription != null)
		{
			itemDescription.Text = Tr($"{seedBagType.ToString().ToUpper()}_SEED_BAG_DESCRIPTION");
		}

		// Update the button to use the correct callback
		var button = itemList.GetNode<Button>("Button");
		if (button != null)
		{
			// Remove any existing signals
			foreach (var connection in button.GetSignalConnectionList("pressed"))
			{
				button.Disconnect("pressed", connection["callable"].AsCallable());
			}

			// Connect the new signal
			button.Pressed += () => OnSeedBagSelected(seedBagType);
		}

		// Insert at the specified index instead of adding at the end
		vboxContainer.AddChild(itemList);
		vboxContainer.MoveChild(itemList, insertIndex);

		// Store references
		_seedBagItemLists[seedBagType] = itemList;
		_seedBagButtons[seedBagType] = button;

		GD.Print($"SeedMakerMenu: Created dynamic ItemList for {seedBagType} at index {insertIndex}");
	}



	private void CreateNextUnlockInfoItemList(VBoxContainer vboxContainer)
	{
		// Get the template ItemList to duplicate
		var templateItemList = GetNode<ItemList>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag");

		// Duplicate the entire ItemList
		var itemList = (ItemList)templateItemList.Duplicate();
		itemList.Name = "ItemListNextUnlockInfo";

		// Remove the button since we don't need it for the info display
		var button = itemList.GetNode<Button>("Button");
		if (button != null)
		{
			button.QueueFree();
		}

		// Remove price icons and labels
		var priceIcon2 = itemList.GetNode<Sprite2D>("PriceIcon2");
		if (priceIcon2 != null) priceIcon2.QueueFree();

		var price2 = itemList.GetNode<Label>("Price2");
		if (price2 != null) price2.QueueFree();

		var priceIcon3 = itemList.GetNode<Sprite2D>("PriceIcon3");
		if (priceIcon3 != null) priceIcon3.QueueFree();

		var price3 = itemList.GetNode<Label>("Price3");
		if (price3 != null) price3.QueueFree();

		var buildButton = itemList.GetNode<Sprite2D>("BuildButton");
		if (buildButton != null) buildButton.QueueFree();

		// Update the item name label to show the unlock info
		var itemName = itemList.GetNode<Label>("ItemName");
		if (itemName != null)
		{
			itemName.Text = Tr("NEXT_UNLOCK_INFO");
			_nextUnlockInfoLabel = itemName;
		}

		// Hide the description
		var itemDescription = itemList.GetNode<Label>("ItemDescription");
		if (itemDescription != null)
		{
			itemDescription.Visible = false;
		}

		// Add to container and store reference
		vboxContainer.AddChild(itemList);
		_nextUnlockInfoItemList = itemList;

		GD.Print("SeedMakerMenu: Created NextUnlockInfo ItemList");
	}

	private void OnSeedBagSelected(ResourceType seedBagType)
	{
		_selectedResource = seedBagType;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void UpdateSeedBagVisibility()
	{
		if (_currentSeedMaker == null) return;

		var unlockedSeedBags = _currentSeedMaker.GetUnlockedSeedBags();
		GD.Print($"SeedMakerMenu: Unlocked seed bags count: {unlockedSeedBags.Count}");

		foreach (var kvp in _seedBagItemLists)
		{
			var seedBagType = kvp.Key;
			var itemList = kvp.Value;

			if (itemList != null)
			{
				bool isUnlocked = unlockedSeedBags.Contains(seedBagType);
				itemList.Visible = isUnlocked;
				GD.Print($"SeedMakerMenu: {seedBagType} visibility: {isUnlocked}");
			}
		}

		// Update NextUnlockInfo and ensure it's at the end
		UpdateNextUnlockInfo(unlockedSeedBags);

		// Move NextUnlockInfo to the very end of the container
		if (_nextUnlockInfoItemList != null && _nextUnlockInfoItemList.Visible)
		{
			var vboxContainer = GetNode<VBoxContainer>("Control/Panel/ScrollContainer/VBoxContainer");
			vboxContainer.MoveChild(_nextUnlockInfoItemList, -1); // Move to end
		}
	}

	private void UpdateNextUnlockInfo(List<ResourceType> unlockedSeedBags)
	{
		if (_nextUnlockInfoItemList == null || _nextUnlockInfoLabel == null) return;

		// Get all seed bag types in order
		var allSeedBagTypes = new ResourceType[]
		{
			ResourceType.CarrotSeedBag, ResourceType.TurnipSeedBag, ResourceType.PumpkinSeedBag, ResourceType.PotatoSeedBag,
			ResourceType.OnionSeedBag, ResourceType.StrawberrySeedBag, ResourceType.CauliflowerSeedBag, ResourceType.TomatoSeedBag,
			ResourceType.ParsnipSeedBag, ResourceType.SnapPeasSeedBag, ResourceType.GarlicSeedBag, ResourceType.RadishSeedBag,
			ResourceType.CornSeedBag, ResourceType.LeekSeedBag, ResourceType.WheatSeedBag, ResourceType.SunflowerSeedBag,
			ResourceType.BeetrootSeedBag, ResourceType.CabbageSeedBag, ResourceType.RedCabbageSeedBag, ResourceType.BroccoliSeedBag,
			ResourceType.BrusselsSproutSeedBag, ResourceType.RedBellPepperSeedBag, ResourceType.SpinachSeedBag, ResourceType.BokChoySeedBag,
			ResourceType.ArtichokeSeedBag, ResourceType.CottonSeedBag, ResourceType.PurpleGrapesSeedBag, ResourceType.GreenGrapesSeedBag,
			ResourceType.RedGrapesSeedBag, ResourceType.PinkGrapesSeedBag, ResourceType.CantaloupeSeedBag, ResourceType.HoneydewSeedBag,
			ResourceType.ButternutSquashSeedBag, ResourceType.BuckwheatSeedBag, ResourceType.YellowBellPepperSeedBag, ResourceType.OrangeBellPepperSeedBag,
			ResourceType.PurpleBellPepperSeedBag, ResourceType.WhiteBellPepperSeedBag, ResourceType.CoffeeSeedBag, ResourceType.AmaranthSeedBag,
			ResourceType.GlassGemCornSeedBag, ResourceType.GreenChilliPepperSeedBag, ResourceType.RedChilliPepperSeedBag, ResourceType.YellowChilliPepperSeedBag,
			ResourceType.OrangeChilliPepperSeedBag, ResourceType.PurpleChilliPepperSeedBag
		};

		// Check if all seed bags are unlocked
		if (unlockedSeedBags.Count >= allSeedBagTypes.Length)
		{
			_nextUnlockInfoItemList.Visible = false;
			GD.Print("SeedMakerMenu: All seed bags unlocked, hiding NextUnlockInfo");
			return;
		}

		// Use the SeedMaker's GetNextUnlockProgress method
		var (nextSeedBag, requiredSeedBag, currentCount, requiredCount) = _currentSeedMaker.GetNextUnlockProgress();

		if (nextSeedBag != ResourceType.None && requiredSeedBag != ResourceType.None)
		{
			var remainingCount = Math.Max(0, requiredCount - currentCount);

			if (remainingCount > 0)
			{
				// Get the name of the seed bag that needs to be planted more
				var requiredSeedBagName = Tr($"{requiredSeedBag.ToString().ToUpper()}_SEED_BAG_TEXT");
				_nextUnlockInfoLabel.Text = Tr("PLANT_MORE_TO_UNLOCK")
					.Replace("{count}", remainingCount.ToString())
					.Replace("{plant}", requiredSeedBagName);

				_nextUnlockInfoItemList.Visible = true;
				GD.Print($"SeedMakerMenu: NextUnlockInfo - Plant {remainingCount} more {requiredSeedBag} to unlock {nextSeedBag}");
			}
			else
			{
				// Should unlock the next seed bag now
				_nextUnlockInfoItemList.Visible = false;
			}
		}
		else
		{
			// No more seed bags to unlock
			_nextUnlockInfoItemList.Visible = false;
			GD.Print("SeedMakerMenu: No more seed bags to unlock, hiding NextUnlockInfo");
		}
	}

	private void OnButtonMinusOnePressed()
	{
		_selectedAmount = Mathf.Max(0, _selectedAmount - 1);
		UpdateInfoBoard();
	}

	private void OnButtonPlusOnePressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = Mathf.Min(maxAffordable, _selectedAmount + 1);
		UpdateInfoBoard();
	}

	private void OnButtonSetOnePressed()
	{
		_selectedAmount = Mathf.Min(1, GetMaxAffordableAmount());
		UpdateInfoBoard();
	}

	private void OnButtonSet25PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 4) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSet50PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 2) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSetMaxPressed()
	{
		_selectedAmount = GetMaxAffordableAmount();
		UpdateInfoBoard();
	}

	private void OnButtonProducePressed()
	{
		if (_selectedResource == ResourceType.None || _selectedAmount <= 0 || _currentSeedMaker == null)
			return;

		// Verify player has enough resources before starting production
		int maxAffordable = GetMaxAffordableAmount();
		if (maxAffordable < _selectedAmount)
		{
			// Not enough resources, set amount to 0 and update display
			_selectedAmount = 0;
			UpdateInfoBoard();
			return;
		}

		_currentSeedMaker.StartCrafting(_selectedResource, _selectedAmount);

		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("SeedMakerMenu");
		}
		else
		{
			CloseMenu();
		}
	}

	private void UpdateInfoBoard()
	{
		if (_itemFront == null || _amountToProduce == null) return;

		if (_selectedResource != ResourceType.None)
		{
			var texture = TextureManager.Instance?.GetResourceTexture(_selectedResource);
			_itemFront.Texture = texture;
			_itemFront.Visible = true;
		}
		else
		{
			_itemFront.Visible = false;
		}

		_amountToProduce.Text = _selectedAmount.ToString();
	}

	private int GetMaxAffordableAmount()
	{
		if (_selectedResource == ResourceType.None) return 0;
		if (_currentSeedMaker == null) return 0;

		// Check if the seed bag is unlocked
		if (!_currentSeedMaker.CanCraft(_selectedResource, 1))
			return 0;

		// All seed bags require 1 BaseSeed + 1 SeedBag
		int baseSeeds = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.BaseSeed);
		int seedBags = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.SeedBag);
		int maxAmount = Mathf.Min(baseSeeds, seedBags);

		return Mathf.Max(0, maxAmount);
	}

	private void UpdateUnlockProgress()
	{
		if (_currentSeedMaker == null || _unlockProgressLabel == null) return;

		var (nextSeedBag, requiredSeedBag, currentCount, requiredCount) = _currentSeedMaker.GetNextUnlockProgress();

		if (nextSeedBag == ResourceType.None)
		{
			// All seed bags are unlocked
			_unlockProgressLabel.Text = "All seed bags unlocked!";
			_unlockProgressLabel.Visible = true;
		}
		else
		{
			// Show progress towards next unlock
			int remaining = requiredCount - currentCount;
			string nextSeedBagName = ItemInformation.GetResourceInfo(nextSeedBag).Title;
			string requiredSeedBagName = ItemInformation.GetResourceInfo(requiredSeedBag).Title;

			_unlockProgressLabel.Text = $"Craft {remaining} more {requiredSeedBagName} to unlock {nextSeedBagName}";
			_unlockProgressLabel.Visible = true;
		}
	}

	public void OpenMenu(SeedMaker seedMaker)
	{
		_currentSeedMaker = seedMaker;

		// Disable player movement when menu opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		// Make sure panel is visible before playing animation
		var panel = GetNode<Node2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = true;
		}

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}

		UpdateSeedBagVisibility();
		UpdateInfoBoard();
		UpdateUnlockProgress();
		GD.Print("SeedMakerMenu: Menu opened");
	}

	public void CloseMenu()
	{
		_currentSeedMaker = null;

		// Re-enable player movement when menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
			// Connect to animation finished to hide panel after close animation
			if (!_animationPlayer.IsConnected(AnimationPlayer.SignalName.AnimationFinished, Callable.From<StringName>(OnCloseAnimationFinished)))
			{
				_animationPlayer.AnimationFinished += OnCloseAnimationFinished;
			}
		}
		else
		{
			// Fallback if no animation player
			var panel = GetNode<Node2D>("Control/Panel");
			if (panel != null)
			{
				panel.Visible = false;
			}
		}

		GD.Print("SeedMakerMenu: Menu closed");
	}

	private void OnCloseAnimationFinished(StringName animName)
	{
		if (animName == "Close")
		{
			var panel = GetNode<Node2D>("Control/Panel");
			if (panel != null)
			{
				panel.Visible = false;
			}
			_animationPlayer.AnimationFinished -= OnCloseAnimationFinished;
		}
	}

	// IMenu interface implementation
	void IMenu.OpenMenu()
	{
		// Default implementation - requires seed maker to be set externally
		if (_currentSeedMaker != null)
		{
			OpenMenu(_currentSeedMaker);
		}
	}

}
