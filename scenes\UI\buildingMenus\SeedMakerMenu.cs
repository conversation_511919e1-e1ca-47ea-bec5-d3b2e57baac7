using Godot;
using System.Collections.Generic;

public partial class SeedMakerMenu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;

	// InfoBoard controls
	private Sprite2D _itemFront;
	private Label _amountToProduce;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;

	// Progress tracking
	private Label _unlockProgressLabel;

	// All seed bag buttons and ItemLists - will be shown/hidden based on unlock status
	private Dictionary<ResourceType, Button> _seedBagButtons = new();
	private Dictionary<ResourceType, ItemList> _seedBagItemLists = new();

	private SeedMaker _currentSeedMaker;
	private ResourceType _selectedResource = ResourceType.None;
	private int _selectedAmount = 0;

	public override void _Ready()
	{
		GD.Print("SeedMakerMenu: Starting _Ready");

		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");

		InitializeExistingSeedBagButtons();

		// InfoBoard controls
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");
		_unlockProgressLabel = GetNode<Label>("Control/Panel/UnlockProgressLabel");

		// Connect signals
		_closeButton.Pressed += OnCloseButtonPressed;

		_buttonMinusOne.Pressed += OnButtonMinusOnePressed;
		_buttonPlusOne.Pressed += OnButtonPlusOnePressed;
		_buttonSetOne.Pressed += OnButtonSetOnePressed;
		_buttonSet25Percent.Pressed += OnButtonSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnButtonSet50PercentPressed;
		_buttonSetMax.Pressed += OnButtonSetMaxPressed;
		_buttonProduce.Pressed += OnButtonProducePressed;

		// Set Control/Panel to initially hidden
		var panel = GetNode<Node2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = false;
		}

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}

		UpdateInfoBoard();
		GD.Print("SeedMakerMenu: Ready completed");

		// Register with MenuManager for ESC key handling
		MenuManager.Instance?.RegisterMenu("SeedMakerMenu", this);
	}

	public bool IsMenuOpen()
	{
		var panel = GetNode<Sprite2D>("Control/Panel");
		return panel != null && panel.Visible;
	}

	public void SetSeedMaker(SeedMaker seedMaker)
	{
		_currentSeedMaker = seedMaker;
	}

	private void OnCloseButtonPressed()
	{
		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("SeedMakerMenu");
		}
		else
		{
			CloseMenu();
		}
	}

	private void InitializeExistingSeedBagButtons()
	{
		// Get the VBoxContainer where we'll add all seed bag ItemLists
		var vboxContainer = GetNode<VBoxContainer>("Control/Panel/ScrollContainer/VBoxContainer");

		// Initialize the existing CarrotSeedBag from .tscn file
		try
		{
			var carrotItemList = GetNode<ItemList>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag");
			var carrotButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag/Button");

			if (carrotItemList != null && carrotButton != null)
			{
				_seedBagItemLists[ResourceType.CarrotSeedBag] = carrotItemList;
				_seedBagButtons[ResourceType.CarrotSeedBag] = carrotButton;
				carrotButton.Pressed += () => OnSeedBagSelected(ResourceType.CarrotSeedBag);
				GD.Print("SeedMakerMenu: Initialized CarrotSeedBag button from .tscn");
			}
		}
		catch (System.Exception e)
		{
			GD.PrintErr($"SeedMakerMenu: Failed to initialize CarrotSeedBag: {e.Message}");
		}

		// Create all other seed bag ItemLists dynamically
		CreateDynamicSeedBagItemLists(vboxContainer);
	}

	private void CreateDynamicSeedBagItemLists(VBoxContainer vboxContainer)
	{
		// All seed bags except CarrotSeedBag (which is in .tscn file)
		var seedBagTypes = new ResourceType[]
		{
			ResourceType.TurnipSeedBag, ResourceType.PumpkinSeedBag, ResourceType.PotatoSeedBag,
			ResourceType.OnionSeedBag, ResourceType.StrawberrySeedBag, ResourceType.CauliflowerSeedBag, ResourceType.TomatoSeedBag,
			ResourceType.ParsnipSeedBag, ResourceType.SnapPeasSeedBag, ResourceType.GarlicSeedBag, ResourceType.RadishSeedBag,
			ResourceType.CornSeedBag, ResourceType.LeekSeedBag, ResourceType.WheatSeedBag, ResourceType.SunflowerSeedBag,
			ResourceType.BeetrootSeedBag, ResourceType.CabbageSeedBag, ResourceType.RedCabbageSeedBag, ResourceType.BroccoliSeedBag,
			ResourceType.BrusselsSproutSeedBag, ResourceType.RedBellPepperSeedBag, ResourceType.SpinachSeedBag, ResourceType.BokChoySeedBag,
			ResourceType.ArtichokeSeedBag, ResourceType.CottonSeedBag, ResourceType.PurpleGrapesSeedBag, ResourceType.GreenGrapesSeedBag,
			ResourceType.RedGrapesSeedBag, ResourceType.PinkGrapesSeedBag, ResourceType.CantaloupeSeedBag, ResourceType.HoneydewSeedBag,
			ResourceType.ButternutSquashSeedBag, ResourceType.BuckwheatSeedBag, ResourceType.YellowBellPepperSeedBag, ResourceType.OrangeBellPepperSeedBag,
			ResourceType.PurpleBellPepperSeedBag, ResourceType.WhiteBellPepperSeedBag, ResourceType.CoffeeSeedBag, ResourceType.AmaranthSeedBag,
			ResourceType.GlassGemCornSeedBag, ResourceType.GreenChilliPepperSeedBag, ResourceType.RedChilliPepperSeedBag, ResourceType.YellowChilliPepperSeedBag,
			ResourceType.OrangeChilliPepperSeedBag, ResourceType.PurpleChilliPepperSeedBag
		};

		foreach (var seedBagType in seedBagTypes)
		{
			CreateSeedBagItemList(vboxContainer, seedBagType);
		}
	}

	private void CreateSeedBagItemList(VBoxContainer vboxContainer, ResourceType seedBagType)
	{
		// Create ItemList container
		var itemList = new ItemList();
		itemList.Name = $"ItemList{seedBagType}";
		itemList.CustomMinimumSize = new Vector2(0, 40);
		itemList.SelectMode = ItemList.SelectModeEnum.Single;

		// Get the CarrotSeedBag ItemList to copy its theme styles
		var carrotItemList = GetNode<ItemList>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag");
		CopyItemListTheme(itemList, carrotItemList);

		// Create IconBackground - use the same texture as CarrotSeedBag
		var iconBackground = new Sprite2D();
		iconBackground.Name = "IconBackground";
		iconBackground.Position = new Vector2(17.4737f, 19.3684f);
		var carrotIconBg = carrotItemList.GetNode<Sprite2D>("IconBackground");
		iconBackground.Texture = carrotIconBg.Texture;
		itemList.AddChild(iconBackground);

		// Create IconForeground with the correct seed bag texture
		var iconForeground = new Sprite2D();
		iconForeground.Name = "IconForeground";
		iconForeground.Position = new Vector2(17.4737f, 19.3684f);
		iconForeground.Texture = TextureManager.Instance.GetResourceTexture(seedBagType);
		itemList.AddChild(iconForeground);

		// Create price icons and labels (copy from CarrotSeedBag)
		CreatePriceElements(itemList, carrotItemList);

		// Create build button (copy from CarrotSeedBag)
		var buildButton = new Sprite2D();
		buildButton.Name = "BuildButton";
		buildButton.Position = new Vector2(165.684f, 21.2632f);
		buildButton.Scale = new Vector2(1.49f, 1.49f);
		var carrotBuildButton = carrotItemList.GetNode<Sprite2D>("BuildButton");
		buildButton.Texture = carrotBuildButton.Texture;
		itemList.AddChild(buildButton);

		// Create item name label
		var itemName = new Label();
		itemName.Name = "ItemName";
		itemName.Position = new Vector2(34.0f, 1.0f);
		itemName.Size = new Vector2(144.0f, 16.0f);
		itemName.Scale = new Vector2(0.73f, 0.73f);
		itemName.Text = Tr($"{seedBagType.ToString().ToUpper()}_SEED_BAG_TEXT");
		itemName.HorizontalAlignment = HorizontalAlignment.Left;
		itemList.AddChild(itemName);

		// Create item description label
		var itemDescription = new Label();
		itemDescription.Name = "ItemDescription";
		itemDescription.Position = new Vector2(34.0f, 13.0f);
		itemDescription.Size = new Vector2(160.0f, 51.0f);
		itemDescription.Scale = new Vector2(0.495f, 0.495f);
		itemDescription.Text = Tr($"{seedBagType.ToString().ToUpper()}_SEED_BAG_DESCRIPTION");
		itemDescription.HorizontalAlignment = HorizontalAlignment.Left;
		itemDescription.VerticalAlignment = VerticalAlignment.Top;
		itemList.AddChild(itemDescription);

		// Create button (copy theme from CarrotSeedBag)
		var button = new Button();
		button.Name = "Button";
		button.Position = new Vector2(150.0f, 4.0f);
		button.Size = new Vector2(31.0f, 32.0f);
		var carrotButton = carrotItemList.GetNode<Button>("Button");
		CopyButtonTheme(button, carrotButton);
		button.Pressed += () => OnSeedBagSelected(seedBagType);
		itemList.AddChild(button);

		// Add to container and store references
		vboxContainer.AddChild(itemList);
		_seedBagItemLists[seedBagType] = itemList;
		_seedBagButtons[seedBagType] = button;

		GD.Print($"SeedMakerMenu: Created dynamic ItemList for {seedBagType}");
	}

	private void CopyItemListTheme(ItemList target, ItemList source)
	{
		// Copy all theme overrides from source to target
		var themeOverrides = new string[]
		{
			"focus", "panel", "hovered", "selected_mirrored", "selected",
			"selected_focus", "cursor_unfocused", "cursor"
		};

		foreach (var themeOverride in themeOverrides)
		{
			var sourceStyle = source.GetThemeStylebox(themeOverride);
			if (sourceStyle != null)
			{
				target.AddThemeStyleboxOverride(themeOverride, sourceStyle);
			}
		}
	}

	private void CreatePriceElements(ItemList itemList, ItemList carrotItemList)
	{
		// Copy PriceIcon2
		var priceIcon2 = new Sprite2D();
		priceIcon2.Name = "PriceIcon2";
		priceIcon2.Position = new Vector2(141.32f, 12.4211f);
		var carrotPriceIcon2 = carrotItemList.GetNode<Sprite2D>("PriceIcon2");
		priceIcon2.Texture = carrotPriceIcon2.Texture;
		itemList.AddChild(priceIcon2);

		// Copy Price2 label
		var price2 = new Label();
		price2.Name = "Price2";
		price2.Position = new Vector2(126.737f, 6.31579f);
		price2.Size = new Vector2(84.0f, 17.0f);
		price2.Scale = new Vector2(0.73f, 0.73f);
		price2.Text = "1";
		price2.HorizontalAlignment = HorizontalAlignment.Left;
		itemList.AddChild(price2);

		// Copy PriceIcon3
		var priceIcon3 = new Sprite2D();
		priceIcon3.Name = "PriceIcon3";
		priceIcon3.Position = new Vector2(141.32f, 26.1053f);
		var carrotPriceIcon3 = carrotItemList.GetNode<Sprite2D>("PriceIcon3");
		priceIcon3.Texture = carrotPriceIcon3.Texture;
		itemList.AddChild(priceIcon3);

		// Copy Price3 label
		var price3 = new Label();
		price3.Name = "Price3";
		price3.Position = new Vector2(126.737f, 20.2106f);
		price3.Size = new Vector2(84.0f, 17.0f);
		price3.Scale = new Vector2(0.73f, 0.73f);
		price3.Text = "1";
		price3.HorizontalAlignment = HorizontalAlignment.Left;
		itemList.AddChild(price3);
	}

	private void CopyButtonTheme(Button target, Button source)
	{
		// Copy all theme overrides from source to target
		var themeOverrides = new string[]
		{
			"focus", "disabled_mirrored", "disabled", "hover_pressed_mirrored",
			"hover_pressed", "hover_mirrored", "hover", "pressed_mirrored",
			"pressed", "normal_mirrored", "normal"
		};

		foreach (var themeOverride in themeOverrides)
		{
			var sourceStyle = source.GetThemeStylebox(themeOverride);
			if (sourceStyle != null)
			{
				target.AddThemeStyleboxOverride(themeOverride, sourceStyle);
			}
		}
	}

	private void OnSeedBagSelected(ResourceType seedBagType)
	{
		_selectedResource = seedBagType;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void UpdateSeedBagVisibility()
	{
		if (_currentSeedMaker == null) return;

		var unlockedSeedBags = _currentSeedMaker.GetUnlockedSeedBags();
		GD.Print($"SeedMakerMenu: Unlocked seed bags count: {unlockedSeedBags.Count}");

		foreach (var kvp in _seedBagItemLists)
		{
			var seedBagType = kvp.Key;
			var itemList = kvp.Value;

			if (itemList != null)
			{
				bool isUnlocked = unlockedSeedBags.Contains(seedBagType);
				itemList.Visible = isUnlocked;
				GD.Print($"SeedMakerMenu: {seedBagType} visibility: {isUnlocked}");
			}
		}
	}

	private void OnButtonMinusOnePressed()
	{
		_selectedAmount = Mathf.Max(0, _selectedAmount - 1);
		UpdateInfoBoard();
	}

	private void OnButtonPlusOnePressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = Mathf.Min(maxAffordable, _selectedAmount + 1);
		UpdateInfoBoard();
	}

	private void OnButtonSetOnePressed()
	{
		_selectedAmount = Mathf.Min(1, GetMaxAffordableAmount());
		UpdateInfoBoard();
	}

	private void OnButtonSet25PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 4) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSet50PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 2) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSetMaxPressed()
	{
		_selectedAmount = GetMaxAffordableAmount();
		UpdateInfoBoard();
	}

	private void OnButtonProducePressed()
	{
		if (_selectedResource == ResourceType.None || _selectedAmount <= 0 || _currentSeedMaker == null)
			return;

		// Verify player has enough resources before starting production
		int maxAffordable = GetMaxAffordableAmount();
		if (maxAffordable < _selectedAmount)
		{
			// Not enough resources, set amount to 0 and update display
			_selectedAmount = 0;
			UpdateInfoBoard();
			return;
		}

		_currentSeedMaker.StartCrafting(_selectedResource, _selectedAmount);

		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("SeedMakerMenu");
		}
		else
		{
			CloseMenu();
		}
	}

	private void UpdateInfoBoard()
	{
		if (_itemFront == null || _amountToProduce == null) return;

		if (_selectedResource != ResourceType.None)
		{
			var texture = TextureManager.Instance?.GetResourceTexture(_selectedResource);
			_itemFront.Texture = texture;
			_itemFront.Visible = true;
		}
		else
		{
			_itemFront.Visible = false;
		}

		_amountToProduce.Text = _selectedAmount.ToString();
	}

	private int GetMaxAffordableAmount()
	{
		if (_selectedResource == ResourceType.None) return 0;
		if (_currentSeedMaker == null) return 0;

		// Check if the seed bag is unlocked
		if (!_currentSeedMaker.CanCraft(_selectedResource, 1))
			return 0;

		// All seed bags require 1 BaseSeed + 1 SeedBag
		int baseSeeds = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.BaseSeed);
		int seedBags = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.SeedBag);
		int maxAmount = Mathf.Min(baseSeeds, seedBags);

		return Mathf.Max(0, maxAmount);
	}

	private void UpdateUnlockProgress()
	{
		if (_currentSeedMaker == null || _unlockProgressLabel == null) return;

		var (nextSeedBag, requiredSeedBag, currentCount, requiredCount) = _currentSeedMaker.GetNextUnlockProgress();

		if (nextSeedBag == ResourceType.None)
		{
			// All seed bags are unlocked
			_unlockProgressLabel.Text = "All seed bags unlocked!";
			_unlockProgressLabel.Visible = true;
		}
		else
		{
			// Show progress towards next unlock
			int remaining = requiredCount - currentCount;
			string nextSeedBagName = ItemInformation.GetResourceInfo(nextSeedBag).Title;
			string requiredSeedBagName = ItemInformation.GetResourceInfo(requiredSeedBag).Title;

			_unlockProgressLabel.Text = $"Craft {remaining} more {requiredSeedBagName} to unlock {nextSeedBagName}";
			_unlockProgressLabel.Visible = true;
		}
	}

	public void OpenMenu(SeedMaker seedMaker)
	{
		_currentSeedMaker = seedMaker;

		// Disable player movement when menu opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		// Make sure panel is visible before playing animation
		var panel = GetNode<Node2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = true;
		}

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}

		UpdateSeedBagVisibility();
		UpdateInfoBoard();
		UpdateUnlockProgress();
		GD.Print("SeedMakerMenu: Menu opened");
	}

	public void CloseMenu()
	{
		_currentSeedMaker = null;

		// Re-enable player movement when menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
			// Connect to animation finished to hide panel after close animation
			if (!_animationPlayer.IsConnected(AnimationPlayer.SignalName.AnimationFinished, Callable.From<StringName>(OnCloseAnimationFinished)))
			{
				_animationPlayer.AnimationFinished += OnCloseAnimationFinished;
			}
		}
		else
		{
			// Fallback if no animation player
			var panel = GetNode<Node2D>("Control/Panel");
			if (panel != null)
			{
				panel.Visible = false;
			}
		}

		GD.Print("SeedMakerMenu: Menu closed");
	}

	private void OnCloseAnimationFinished(StringName animName)
	{
		if (animName == "Close")
		{
			var panel = GetNode<Node2D>("Control/Panel");
			if (panel != null)
			{
				panel.Visible = false;
			}
			_animationPlayer.AnimationFinished -= OnCloseAnimationFinished;
		}
	}

	// IMenu interface implementation
	void IMenu.OpenMenu()
	{
		// Default implementation - requires seed maker to be set externally
		if (_currentSeedMaker != null)
		{
			OpenMenu(_currentSeedMaker);
		}
	}

}
