using Godot;
using System.Collections.Generic;

public enum DescriptionType
{
    TextOnly
}

public class ItemInfo
{
    public string Title { get; set; }
    public string Description { get; set; }
    public DescriptionType DescriptionType { get; set; }
    public bool CanBeUsed { get; set; }
    public bool CanAssignToQuickUse { get; set; }
    public int SellPrice { get; set; } = 0;
    public int BuyPrice { get; set; } = 0;
}

public static class ItemInformation
{
    private static readonly Dictionary<ResourceType, ItemInfo> _resourceInfo = new Dictionary<ResourceType, ItemInfo>
    {
        {
            ResourceType.Wood,
            new ItemInfo
            {
                Title = "TEXT_WOOD_TITLE",
                Description = "TEXT_WOOD_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 1,
                BuyPrice = 2
            }
        },
        {
            ResourceType.Stone,
            new ItemInfo
            {
                Title = "TEXT_STONE_TITLE",
                Description = "TEXT_STONE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 1,
                BuyPrice = 2
            }
        },
        {
            ResourceType.Net,
            new ItemInfo
            {
                Title = "TEXT_NET_TITLE",
                Description = "TEXT_NET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = false,
                SellPrice = 5,
                BuyPrice = 10
            }
        },
        {
            ResourceType.Plank,
            new ItemInfo
            {
                Title = "PLANK_TEXT",
                Description = "PLANK_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Stone2,
            new ItemInfo
            {
                Title = "STONE2_TEXT",
                Description = "STONE2_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Berry,
            new ItemInfo
            {
                Title = "BERRY_TEXT",
                Description = "BERRY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Leaf,
            new ItemInfo
            {
                Title = "LEAF_TEXT",
                Description = "LEAF_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 1,
                BuyPrice = 2
            }
        },
        {
            ResourceType.Branch,
            new ItemInfo
            {
                Title = "BRANCH_TEXT",
                Description = "BRANCH_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.CopperOre,
            new ItemInfo
            {
                Title = "COPPER_ORE_TEXT",
                Description = "COPPER_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.IronOre,
            new ItemInfo
            {
                Title = "IRON_ORE_TEXT",
                Description = "IRON_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.GoldOre,
            new ItemInfo
            {
                Title = "GOLD_ORE_TEXT",
                Description = "GOLD_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 8,
                BuyPrice = 16
            }
        },
        {
            ResourceType.IndigosiumOre,
            new ItemInfo
            {
                Title = "INDIGOSIUM_ORE_TEXT",
                Description = "INDIGOSIUM_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 12,
                BuyPrice = 24
            }
        },
        {
            ResourceType.MithrilOre,
            new ItemInfo
            {
                Title = "MITHRIL_ORE_TEXT",
                Description = "MITHRIL_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 16,
                BuyPrice = 32
            }
        },
        {
            ResourceType.ErithrydiumOre,
            new ItemInfo
            {
                Title = "ERITHRYDIUM_ORE_TEXT",
                Description = "ERITHRYDIUM_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 20,
                BuyPrice = 40
            }
        },
        {
            ResourceType.AdamantiteOre,
            new ItemInfo
            {
                Title = "ADAMANTITE_ORE_TEXT",
                Description = "ADAMANTITE_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 25,
                BuyPrice = 50
            }
        },
        {
            ResourceType.UraniumOre,
            new ItemInfo
            {
                Title = "URANIUM_ORE_TEXT",
                Description = "URANIUM_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 30,
                BuyPrice = 60
            }
        },
        {
            ResourceType.CopperBar,
            new ItemInfo
            {
                Title = "COPPER_BAR_TEXT",
                Description = "COPPER_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 6,
                BuyPrice = 12
            }
        },
        {
            ResourceType.IronBar,
            new ItemInfo
            {
                Title = "IRON_BAR_TEXT",
                Description = "IRON_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 8,
                BuyPrice = 16
            }
        },
        {
            ResourceType.GoldBar,
            new ItemInfo
            {
                Title = "GOLD_BAR_TEXT",
                Description = "GOLD_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 16,
                BuyPrice = 32
            }
        },
        {
            ResourceType.IndigosiumBar,
            new ItemInfo
            {
                Title = "INDIGOSIUM_BAR_TEXT",
                Description = "INDIGOSIUM_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 24,
                BuyPrice = 48
            }
        },
        {
            ResourceType.MithrilBar,
            new ItemInfo
            {
                Title = "MITHRIL_BAR_TEXT",
                Description = "MITHRIL_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 32,
                BuyPrice = 64
            }
        },
        {
            ResourceType.ErithrydiumBar,
            new ItemInfo
            {
                Title = "ERITHRYDIUM_BAR_TEXT",
                Description = "ERITHRYDIUM_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 40,
                BuyPrice = 80
            }
        },
        {
            ResourceType.AdamantiteBar,
            new ItemInfo
            {
                Title = "ADAMANTITE_BAR_TEXT",
                Description = "ADAMANTITE_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 50,
                BuyPrice = 100
            }
        },
        {
            ResourceType.UraniumBar,
            new ItemInfo
            {
                Title = "URANIUM_BAR_TEXT",
                Description = "URANIUM_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 60,
                BuyPrice = 120
            }
        },
        {
            ResourceType.CopperSheet,
            new ItemInfo
            {
                Title = "COPPER_SHEET_TEXT",
                Description = "COPPER_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 12,
                BuyPrice = 24
            }
        },
        {
            ResourceType.IronSheet,
            new ItemInfo
            {
                Title = "IRON_SHEET_TEXT",
                Description = "IRON_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 16,
                BuyPrice = 32
            }
        },
        {
            ResourceType.GoldSheet,
            new ItemInfo
            {
                Title = "GOLD_SHEET_TEXT",
                Description = "GOLD_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 32,
                BuyPrice = 64
            }
        },
        {
            ResourceType.IndigosiumSheet,
            new ItemInfo
            {
                Title = "INDIGOSIUM_SHEET_TEXT",
                Description = "INDIGOSIUM_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 48,
                BuyPrice = 96
            }
        },
        {
            ResourceType.MithrilSheet,
            new ItemInfo
            {
                Title = "MITHRIL_SHEET_TEXT",
                Description = "MITHRIL_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 64,
                BuyPrice = 128
            }
        },
        {
            ResourceType.ErithrydiumSheet,
            new ItemInfo
            {
                Title = "ERITHRYDIUM_SHEET_TEXT",
                Description = "ERITHRYDIUM_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 80,
                BuyPrice = 160
            }
        },
        {
            ResourceType.AdamantiteSheet,
            new ItemInfo
            {
                Title = "ADAMANTITE_SHEET_TEXT",
                Description = "ADAMANTITE_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 100,
                BuyPrice = 200
            }
        },
        {
            ResourceType.UraniumSheet,
            new ItemInfo
            {
                Title = "URANIUM_SHEET_TEXT",
                Description = "URANIUM_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 120,
                BuyPrice = 240
            }
        },
        {
            ResourceType.WoodenBeam,
            new ItemInfo
            {
                Title = "WOODEN_BEAM_TEXT",
                Description = "WOODEN_BEAM_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.WoodenStick,
            new ItemInfo
            {
                Title = "STICK_TEXT",
                Description = "STICK_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.RawRabbitLeg,
            new ItemInfo
            {
                Title = "RAW_RABBIT_LEG_TEXT",
                Description = "RAW_RABBIT_LEG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.CookedRabbitLeg,
            new ItemInfo
            {
                Title = "COOKED_RABBIT_LEG_TEXT",
                Description = "COOKED_RABBIT_LEG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 8,
                BuyPrice = 16
            }
        },
        {
            ResourceType.WoodenKey,
            new ItemInfo
            {
                Title = "WOODEN_KEY_TEXT",
                Description = "WOODEN_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 10,
                BuyPrice = 20
            }
        },
        {
            ResourceType.StoneBrick,
            new ItemInfo
            {
                Title = "TEXT_STONE_BRICK_TITLE",
                Description = "TEXT_STONE_BRICK_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Stone2Brick,
            new ItemInfo
            {
                Title = "TEXT_STONE2_BRICK_TITLE",
                Description = "TEXT_STONE2_BRICK_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Nails,
            new ItemInfo
            {
                Title = "TEXT_NAILS_TITLE",
                Description = "TEXT_NAILS_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Arrow,
            new ItemInfo
            {
                Title = "TEXT_ARROW_TITLE",
                Description = "TEXT_ARROW_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.CopperKey,
            new ItemInfo
            {
                Title = "COPPER_KEY_TEXT",
                Description = "COPPER_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 15,
                BuyPrice = 30
            }
        },
        {
            ResourceType.IronKey,
            new ItemInfo
            {
                Title = "IRON_KEY_TEXT",
                Description = "IRON_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 20,
                BuyPrice = 40
            }
        },
        {
            ResourceType.GoldKey,
            new ItemInfo
            {
                Title = "GOLD_KEY_TEXT",
                Description = "GOLD_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 30,
                BuyPrice = 60
            }
        },
        {
            ResourceType.IndigosiumKey,
            new ItemInfo
            {
                Title = "INDIGOSIUM_KEY_TEXT",
                Description = "INDIGOSIUM_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 40,
                BuyPrice = 80
            }
        },
        {
            ResourceType.MithrilKey,
            new ItemInfo
            {
                Title = "MITHRIL_KEY_TEXT",
                Description = "MITHRIL_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 50,
                BuyPrice = 100
            }
        },
        {
            ResourceType.ErithrydiumKey,
            new ItemInfo
            {
                Title = "ERITHRYDIUM_KEY_TEXT",
                Description = "ERITHRYDIUM_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 60,
                BuyPrice = 120
            }
        },
        {
            ResourceType.AdamantiteKey,
            new ItemInfo
            {
                Title = "ADAMANTITE_KEY_TEXT",
                Description = "ADAMANTITE_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 70,
                BuyPrice = 140
            }
        },
        {
            ResourceType.UraniumKey,
            new ItemInfo
            {
                Title = "URANIUM_KEY_TEXT",
                Description = "URANIUM_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 80,
                BuyPrice = 160
            }
        },
        {
            ResourceType.Charcoal,
            new ItemInfo
            {
                Title = "CHARCOAL_TEXT",
                Description = "CHARCOAL_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.BrownMushroom,
            new ItemInfo
            {
                Title = "BROWN_MUSHROOM_TEXT",
                Description = "BROWN_MUSHROOM_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.BlueMushroom,
            new ItemInfo
            {
                Title = "BLUE_MUSHROOM_TEXT",
                Description = "BLUE_MUSHROOM_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.RedMushroom,
            new ItemInfo
            {
                Title = "RED_MUSHROOM_TEXT",
                Description = "RED_MUSHROOM_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.VioletMushroom,
            new ItemInfo
            {
                Title = "VIOLET_MUSHROOM_TEXT",
                Description = "VIOLET_MUSHROOM_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        // Plants
        {
            ResourceType.Carrot,
            new ItemInfo
            {
                Title = "CARROT_TEXT",
                Description = "CARROT_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Turnip,
            new ItemInfo
            {
                Title = "TURNIP_TEXT",
                Description = "TURNIP_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Pumpkin,
            new ItemInfo
            {
                Title = "PUMPKIN_TEXT",
                Description = "PUMPKIN_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Potato,
            new ItemInfo
            {
                Title = "POTATO_TEXT",
                Description = "POTATO_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Onion,
            new ItemInfo
            {
                Title = "ONION_TEXT",
                Description = "ONION_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Strawberry,
            new ItemInfo
            {
                Title = "STRAWBERRY_TEXT",
                Description = "STRAWBERRY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Cauliflower,
            new ItemInfo
            {
                Title = "CAULIFLOWER_TEXT",
                Description = "CAULIFLOWER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Tomato,
            new ItemInfo
            {
                Title = "TOMATO_TEXT",
                Description = "TOMATO_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Parsnip,
            new ItemInfo
            {
                Title = "PARSNIP_TEXT",
                Description = "PARSNIP_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.SnapPeas,
            new ItemInfo
            {
                Title = "SNAP_PEAS_TEXT",
                Description = "SNAP_PEAS_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Garlic,
            new ItemInfo
            {
                Title = "GARLIC_TEXT",
                Description = "GARLIC_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Radish,
            new ItemInfo
            {
                Title = "RADISH_TEXT",
                Description = "RADISH_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Corn,
            new ItemInfo
            {
                Title = "CORN_TEXT",
                Description = "CORN_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Leek,
            new ItemInfo
            {
                Title = "LEEK_TEXT",
                Description = "LEEK_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Wheat,
            new ItemInfo
            {
                Title = "WHEAT_TEXT",
                Description = "WHEAT_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Sunflower,
            new ItemInfo
            {
                Title = "SUNFLOWER_TEXT",
                Description = "SUNFLOWER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Beetroot,
            new ItemInfo
            {
                Title = "BEETROOT_TEXT",
                Description = "BEETROOT_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Cabbage,
            new ItemInfo
            {
                Title = "CABBAGE_TEXT",
                Description = "CABBAGE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.RedCabbage,
            new ItemInfo
            {
                Title = "RED_CABBAGE_TEXT",
                Description = "RED_CABBAGE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Broccoli,
            new ItemInfo
            {
                Title = "BROCCOLI_TEXT",
                Description = "BROCCOLI_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.BrusselsSprout,
            new ItemInfo
            {
                Title = "BRUSSELS_SPROUT_TEXT",
                Description = "BRUSSELS_SPROUT_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.RedBellPepper,
            new ItemInfo
            {
                Title = "RED_BELL_PEPPER_TEXT",
                Description = "RED_BELL_PEPPER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Spinach,
            new ItemInfo
            {
                Title = "SPINACH_TEXT",
                Description = "SPINACH_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.BokChoy,
            new ItemInfo
            {
                Title = "BOK_CHOY_TEXT",
                Description = "BOK_CHOY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Artichoke,
            new ItemInfo
            {
                Title = "ARTICHOKE_TEXT",
                Description = "ARTICHOKE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.Cotton,
            new ItemInfo
            {
                Title = "COTTON_TEXT",
                Description = "COTTON_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.PurpleGrapes,
            new ItemInfo
            {
                Title = "PURPLE_GRAPES_TEXT",
                Description = "PURPLE_GRAPES_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.GreenGrapes,
            new ItemInfo
            {
                Title = "GREEN_GRAPES_TEXT",
                Description = "GREEN_GRAPES_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.RedGrapes,
            new ItemInfo
            {
                Title = "RED_GRAPES_TEXT",
                Description = "RED_GRAPES_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.PinkGrapes,
            new ItemInfo
            {
                Title = "PINK_GRAPES_TEXT",
                Description = "PINK_GRAPES_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.Cantaloupe,
            new ItemInfo
            {
                Title = "CANTALOUPE_TEXT",
                Description = "CANTALOUPE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.Honeydew,
            new ItemInfo
            {
                Title = "HONEYDEW_TEXT",
                Description = "HONEYDEW_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.ButternutSquash,
            new ItemInfo
            {
                Title = "BUTTERNUT_SQUASH_TEXT",
                Description = "BUTTERNUT_SQUASH_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.Buckwheat,
            new ItemInfo
            {
                Title = "BUCKWHEAT_TEXT",
                Description = "BUCKWHEAT_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.YellowBellPepper,
            new ItemInfo
            {
                Title = "YELLOW_BELL_PEPPER_TEXT",
                Description = "YELLOW_BELL_PEPPER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.OrangeBellPepper,
            new ItemInfo
            {
                Title = "ORANGE_BELL_PEPPER_TEXT",
                Description = "ORANGE_BELL_PEPPER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.PurpleBellPepper,
            new ItemInfo
            {
                Title = "PURPLE_BELL_PEPPER_TEXT",
                Description = "PURPLE_BELL_PEPPER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.WhiteBellPepper,
            new ItemInfo
            {
                Title = "WHITE_BELL_PEPPER_TEXT",
                Description = "WHITE_BELL_PEPPER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.Coffee,
            new ItemInfo
            {
                Title = "COFFEE_TEXT",
                Description = "COFFEE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 5,
                BuyPrice = 10
            }
        },
        {
            ResourceType.Amaranth,
            new ItemInfo
            {
                Title = "AMARANTH_TEXT",
                Description = "AMARANTH_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.GlassGemCorn,
            new ItemInfo
            {
                Title = "GLASS_GEM_CORN_TEXT",
                Description = "GLASS_GEM_CORN_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 5,
                BuyPrice = 10
            }
        },
        {
            ResourceType.GreenChilliPepper,
            new ItemInfo
            {
                Title = "GREEN_CHILLI_PEPPER_TEXT",
                Description = "GREEN_CHILLI_PEPPER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.RedChilliPepper,
            new ItemInfo
            {
                Title = "RED_CHILLI_PEPPER_TEXT",
                Description = "RED_CHILLI_PEPPER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.YellowChilliPepper,
            new ItemInfo
            {
                Title = "YELLOW_CHILLI_PEPPER_TEXT",
                Description = "YELLOW_CHILLI_PEPPER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.OrangeChilliPepper,
            new ItemInfo
            {
                Title = "ORANGE_CHILLI_PEPPER_TEXT",
                Description = "ORANGE_CHILLI_PEPPER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.PurpleChilliPepper,
            new ItemInfo
            {
                Title = "PURPLE_CHILLI_PEPPER_TEXT",
                Description = "PURPLE_CHILLI_PEPPER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 5,
                BuyPrice = 10
            }
        },
        // Seed Bags
        {
            ResourceType.CarrotSeedBag,
            new ItemInfo
            {
                Title = "CARROT_SEED_BAG_TEXT",
                Description = "CARROT_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 1,
                BuyPrice = 3
            }
        },
        {
            ResourceType.TurnipSeedBag,
            new ItemInfo
            {
                Title = "TURNIP_SEED_BAG_TEXT",
                Description = "TURNIP_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 1,
                BuyPrice = 3
            }
        },
        {
            ResourceType.PumpkinSeedBag,
            new ItemInfo
            {
                Title = "PUMPKIN_SEED_BAG_TEXT",
                Description = "PUMPKIN_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.PotatoSeedBag,
            new ItemInfo
            {
                Title = "POTATO_SEED_BAG_TEXT",
                Description = "POTATO_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 1,
                BuyPrice = 3
            }
        },
        {
            ResourceType.OnionSeedBag,
            new ItemInfo
            {
                Title = "ONION_SEED_BAG_TEXT",
                Description = "ONION_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 1,
                BuyPrice = 3
            }
        },
        {
            ResourceType.StrawberrySeedBag,
            new ItemInfo
            {
                Title = "STRAWBERRY_SEED_BAG_TEXT",
                Description = "STRAWBERRY_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.CauliflowerSeedBag,
            new ItemInfo
            {
                Title = "CAULIFLOWER_SEED_BAG_TEXT",
                Description = "CAULIFLOWER_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.TomatoSeedBag,
            new ItemInfo
            {
                Title = "TOMATO_SEED_BAG_TEXT",
                Description = "TOMATO_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.ParsnipSeedBag,
            new ItemInfo
            {
                Title = "PARSNIP_SEED_BAG_TEXT",
                Description = "PARSNIP_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 1,
                BuyPrice = 3
            }
        },
        {
            ResourceType.SnapPeasSeedBag,
            new ItemInfo
            {
                Title = "SNAP_PEAS_SEED_BAG_TEXT",
                Description = "SNAP_PEAS_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 1,
                BuyPrice = 3
            }
        },
        {
            ResourceType.GarlicSeedBag,
            new ItemInfo
            {
                Title = "GARLIC_SEED_BAG_TEXT",
                Description = "GARLIC_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.RadishSeedBag,
            new ItemInfo
            {
                Title = "RADISH_SEED_BAG_TEXT",
                Description = "RADISH_SEED_BAG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 1,
                BuyPrice = 3
            }
        }
    };

    private static readonly Dictionary<ToolType, ItemInfo> _toolInfo = new Dictionary<ToolType, ItemInfo>
    {
        {
            ToolType.Pickaxe,
            new ItemInfo
            {
                Title = "TEXT_PICKAXE_TITLE",
                Description = "TEXT_PICKAXE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Sword,
            new ItemInfo
            {
                Title = "TEXT_SWORD_TITLE",
                Description = "TEXT_SWORD_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Bow,
            new ItemInfo
            {
                Title = "TEXT_BOW_TITLE",
                Description = "TEXT_BOW_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Hammer,
            new ItemInfo
            {
                Title = "TEXT_HAMMER_TITLE",
                Description = "TEXT_HAMMER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Hoe,
            new ItemInfo
            {
                Title = "TEXT_HOE_TITLE",
                Description = "TEXT_HOE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.WateringCan,
            new ItemInfo
            {
                Title = "TEXT_WATERING_CAN_TITLE",
                Description = "TEXT_WATERING_CAN_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        }
    };

    public static ItemInfo GetResourceInfo(ResourceType resourceType)
    {
        return _resourceInfo.TryGetValue(resourceType, out var info) ? info : new ItemInfo
        {
            Title = "TEXT_UNKNOWN_ITEM_TITLE",
            Description = "TEXT_UNKNOWN_ITEM_DESCRIPTION",
            DescriptionType = DescriptionType.TextOnly,
            CanBeUsed = false,
            CanAssignToQuickUse = false
        };
    }

    public static ItemInfo GetToolInfo(ToolType toolType)
    {
        return _toolInfo.TryGetValue(toolType, out var info) ? info : new ItemInfo
        {
            Title = "TEXT_UNKNOWN_TOOL_TITLE",
            Description = "TEXT_UNKNOWN_TOOL_DESCRIPTION",
            DescriptionType = DescriptionType.TextOnly,
            CanBeUsed = false,
            CanAssignToQuickUse = false
        };
    }

    public static bool CanBeUsed(ResourceType resourceType)
    {
        return GetResourceInfo(resourceType).CanBeUsed;
    }

    public static bool CanBeUsed(ToolType toolType)
    {
        return GetToolInfo(toolType).CanBeUsed;
    }

    public static bool CanAssignToQuickUse(ResourceType resourceType)
    {
        return GetResourceInfo(resourceType).CanAssignToQuickUse;
    }

    public static bool CanAssignToQuickUse(ToolType toolType)
    {
        return GetToolInfo(toolType).CanAssignToQuickUse;
    }
}
