[gd_scene load_steps=50 format=3 uid="uid://crkjyn76vf7so"]

[ext_resource type="Script" uid="uid://d1v5tjbqw28th" path="res://scenes/UI/buildingMenus/SeedMakerMenu.cs" id="1_loh6d"]
[ext_resource type="Texture2D" uid="uid://cyvhsyv6xia6m" path="res://resources/solaria/UI/build/build_panel.png" id="2_nwq47"]
[ext_resource type="Texture2D" uid="uid://bgpd0wfpx3kvj" path="res://resources/solaria/UI/inventory/inventory_item_single_slot.png" id="3_ehs1v"]
[ext_resource type="Texture2D" uid="uid://ddldk64jho510" path="res://resources/solaria/planting/seedbag_resources/carrot_seedbag.png" id="4_7vw3y"]
[ext_resource type="Texture2D" uid="uid://486dt68qu54c" path="res://resources/solaria/resources/resource_wood.png" id="5_n2s3f"]
[ext_resource type="Texture2D" uid="uid://rbmx7uwcpffw" path="res://resources/solaria/resources/resource_plank.png" id="5_ucr0c"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="6_c883v"]
[ext_resource type="Texture2D" uid="uid://b5wugfo5k4t1g" path="res://resources/solaria/UI/build/buildButton2.png" id="8_nwq47"]
[ext_resource type="Texture2D" uid="uid://clpqdxl0rnm1e" path="res://resources/solaria/UI/build/build_panel_select_amount.png" id="9_ehs1v"]
[ext_resource type="Texture2D" uid="uid://cjyb5i40sh52m" path="res://resources/solaria/planting/seedbag_resources/turnip_seedbag.png" id="9_t8nuk"]
[ext_resource type="Texture2D" uid="uid://dl8bp5u75xxvx" path="res://resources/solaria/UI/build/craft_amount.png" id="10_ucr0c"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="11_axj1h"]
[ext_resource type="Texture2D" uid="uid://b4qihgrhky4rk" path="res://resources/solaria/UI/build/button1.png" id="11_n2s3f"]
[ext_resource type="Texture2D" uid="uid://dvv01dmhem1hh" path="res://resources/solaria/UI/build/button2.png" id="12_ucr0c"]

[sub_resource type="Animation" id="Animation_iw7eh"]
resource_name = "Close"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.09),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_r0oy0"]
resource_name = "Open"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_cna2i"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_v0ypl"]
_data = {
&"Close": SubResource("Animation_iw7eh"),
&"Open": SubResource("Animation_r0oy0"),
&"RESET": SubResource("Animation_cna2i")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_wltr5"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bu41f"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_titi1"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_addpo"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_cfprg"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_4bpbu"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_iq3py"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_yklww"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_xf8fr"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_07hwm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_oxiyu"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_mes8s"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sapyx"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_b4v27"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jlemb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3sn0u"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_kauif"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jurd5"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jpeu1"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_48vcc"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ehs1v"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ucr0c"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_n2s3f"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_c883v"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jhbuc"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_kmqjj"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_2ys84"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_8kbf3"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_axj1h"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_h2igh"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_iluwe"]

[node name="SeedMakerMenu" type="CanvasLayer"]
script = ExtResource("1_loh6d")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_v0ypl")
}

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="Control"]
visible = false
scale = Vector2(0.95, 0.95)
texture = ExtResource("2_nwq47")

[node name="ScrollContainer" type="ScrollContainer" parent="Control/Panel"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -92.0
offset_top = -116.0
offset_right = -99.0
offset_bottom = -124.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Control/Panel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ItemListCarrotSeedBag" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_wltr5")
theme_override_styles/panel = SubResource("StyleBoxFlat_bu41f")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_titi1")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_addpo")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_cfprg")
theme_override_styles/selected = SubResource("StyleBoxEmpty_4bpbu")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_iq3py")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_yklww")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_xf8fr")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("3_ehs1v")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("4_7vw3y")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_nwq47")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag" instance=ExtResource("6_c883v")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 178.0
offset_bottom = 17.0
scale = Vector2(0.73, 0.73)
text = "CARROT_SEED_BAG_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag" instance=ExtResource("6_c883v")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "CARROT_SEED_BAG_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag"]
position = Vector2(141.32, 16.6316)
texture = ExtResource("5_ucr0c")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag" instance=ExtResource("6_c883v")]
layout_mode = 0
offset_left = 126.737
offset_top = 10.7369
offset_right = 210.737
offset_bottom = 27.7369
scale = Vector2(0.73, 0.73)
text = "1"
horizontal_alignment = 0

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag"]
position = Vector2(141.32, 30.3158)
texture = ExtResource("5_n2s3f")

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListCarrotSeedBag" instance=ExtResource("6_c883v")]
layout_mode = 0
offset_left = 126.737
offset_top = 24.4211
offset_right = 210.737
offset_bottom = 41.4211
scale = Vector2(0.73, 0.73)
text = "1"
horizontal_alignment = 0

[node name="ItemListTurnipSeedBag" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_wltr5")
theme_override_styles/panel = SubResource("StyleBoxFlat_bu41f")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_titi1")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_addpo")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_cfprg")
theme_override_styles/selected = SubResource("StyleBoxEmpty_4bpbu")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_iq3py")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_yklww")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_xf8fr")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListTurnipSeedBag"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("3_ehs1v")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListTurnipSeedBag"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("9_t8nuk")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListTurnipSeedBag"]
position = Vector2(141.32, 17.8948)
texture = ExtResource("5_ucr0c")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListTurnipSeedBag" instance=ExtResource("6_c883v")]
layout_mode = 0
offset_left = 126.737
offset_top = 12.0
offset_right = 210.737
offset_bottom = 29.0001
scale = Vector2(0.73, 0.73)
text = "1"
horizontal_alignment = 0

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListTurnipSeedBag"]
position = Vector2(141.32, 31.579)
texture = ExtResource("5_n2s3f")

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListTurnipSeedBag" instance=ExtResource("6_c883v")]
layout_mode = 0
offset_left = 126.737
offset_top = 25.6843
offset_right = 210.737
offset_bottom = 42.6843
scale = Vector2(0.73, 0.73)
text = "1"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListTurnipSeedBag"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_nwq47")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListTurnipSeedBag" instance=ExtResource("6_c883v")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 178.0
offset_bottom = 17.0
scale = Vector2(0.73, 0.73)
text = "TURNIP_SEED_BAG_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListTurnipSeedBag" instance=ExtResource("6_c883v")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 194.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "TURNIP_SEED_BAG_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListTurnipSeedBag"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="InfoBoard" type="Sprite2D" parent="Control/Panel"]
position = Vector2(158.948, -45.249)
scale = Vector2(1.05263, 1.05263)
texture = ExtResource("9_ehs1v")

[node name="ItemBackground" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(0.999268, -51.0135)
texture = ExtResource("3_ehs1v")

[node name="InfoLabel" parent="Control/Panel/InfoBoard" instance=ExtResource("6_c883v")]
offset_left = -52.0006
offset_top = -28.0135
offset_right = 91.9994
offset_bottom = -12.0135
scale = Vector2(0.73, 0.73)
text = "SELECT_AMOUNT"

[node name="ItemFront" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(0.999268, -51.0135)

[node name="SelectAmountBg" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-1.00085, 0.986572)
scale = Vector2(1.5, 1.5)
texture = ExtResource("10_ucr0c")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard" instance=ExtResource("6_c883v")]
offset_left = -15.0006
offset_top = -13.0135
offset_right = 22.9994
offset_bottom = 20.9865
scale = Vector2(0.73, 0.73)
text = "1"

[node name="Amount1" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-37.0006, 35.9866)
scale = Vector2(0.5, 0.5)
texture = ExtResource("11_n2s3f")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/Amount1" instance=ExtResource("6_c883v")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "x1"

[node name="Amount25" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-12.0004, 35.9865)
scale = Vector2(0.5, 0.5)
texture = ExtResource("11_n2s3f")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/Amount25" instance=ExtResource("6_c883v")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "25%"

[node name="Amount50" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(12.9996, 35.9865)
scale = Vector2(0.5, 0.5)
texture = ExtResource("11_n2s3f")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/Amount50" instance=ExtResource("6_c883v")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "50%"

[node name="AmountMax" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(37.9996, 35.9865)
scale = Vector2(0.5, 0.5)
texture = ExtResource("11_n2s3f")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/AmountMax" instance=ExtResource("6_c883v")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "MAX"

[node name="ProduceButton" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-1.00061, 56.9865)
scale = Vector2(0.59, 0.59)
texture = ExtResource("12_ucr0c")

[node name="ProduceLabel" parent="Control/Panel/InfoBoard/ProduceButton" instance=ExtResource("6_c883v")]
anchors_preset = -1
anchor_left = -0.0233037
anchor_top = 0.00794512
anchor_right = 0.757946
anchor_bottom = 0.257945
offset_left = -30.5086
offset_top = -15.2542
offset_right = 7.49143
offset_bottom = 18.7458
scale = Vector2(0.73, 0.73)
text = "PRODUCE_TEXT"
metadata/_edit_use_anchors_ = true

[node name="ButtonMinusOne" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -21.0006
offset_top = 8.98651
offset_right = -7.00061
offset_bottom = 22.9865
theme_override_styles/focus = SubResource("StyleBoxEmpty_ehs1v")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_ucr0c")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_n2s3f")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_c883v")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_jhbuc")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_kmqjj")
theme_override_styles/hover = SubResource("StyleBoxEmpty_2ys84")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_8kbf3")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_axj1h")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_h2igh")
theme_override_styles/normal = SubResource("StyleBoxEmpty_iluwe")

[node name="ButtonPlusOne" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = 4.99915
offset_top = 8.98657
offset_right = 18.9991
offset_bottom = 22.9866
theme_override_styles/focus = SubResource("StyleBoxEmpty_ehs1v")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_ucr0c")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_n2s3f")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_c883v")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_jhbuc")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_kmqjj")
theme_override_styles/hover = SubResource("StyleBoxEmpty_2ys84")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_8kbf3")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_axj1h")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_h2igh")
theme_override_styles/normal = SubResource("StyleBoxEmpty_iluwe")

[node name="ButtonSetOne" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -49.0
offset_top = 28.0
offset_right = -25.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_ehs1v")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_ucr0c")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_n2s3f")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_c883v")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_jhbuc")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_kmqjj")
theme_override_styles/hover = SubResource("StyleBoxEmpty_2ys84")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_8kbf3")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_axj1h")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_h2igh")
theme_override_styles/normal = SubResource("StyleBoxEmpty_iluwe")

[node name="ButtonSet25Percent" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -24.0
offset_top = 28.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_ehs1v")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_ucr0c")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_n2s3f")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_c883v")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_jhbuc")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_kmqjj")
theme_override_styles/hover = SubResource("StyleBoxEmpty_2ys84")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_8kbf3")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_axj1h")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_h2igh")
theme_override_styles/normal = SubResource("StyleBoxEmpty_iluwe")

[node name="ButtonSet50Percent" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = 1.0
offset_top = 28.0
offset_right = 25.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_ehs1v")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_ucr0c")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_n2s3f")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_c883v")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_jhbuc")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_kmqjj")
theme_override_styles/hover = SubResource("StyleBoxEmpty_2ys84")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_8kbf3")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_axj1h")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_h2igh")
theme_override_styles/normal = SubResource("StyleBoxEmpty_iluwe")

[node name="ButtonSetMax" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = 26.0
offset_top = 28.0
offset_right = 50.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_ehs1v")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_ucr0c")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_n2s3f")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_c883v")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_jhbuc")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_kmqjj")
theme_override_styles/hover = SubResource("StyleBoxEmpty_2ys84")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_8kbf3")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_axj1h")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_h2igh")
theme_override_styles/normal = SubResource("StyleBoxEmpty_iluwe")

[node name="ButtonProduce" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -20.0
offset_top = 47.0
offset_right = 18.0
offset_bottom = 66.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_ehs1v")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_ucr0c")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_n2s3f")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_c883v")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_jhbuc")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_kmqjj")
theme_override_styles/hover = SubResource("StyleBoxEmpty_2ys84")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_8kbf3")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_axj1h")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_h2igh")
theme_override_styles/normal = SubResource("StyleBoxEmpty_iluwe")

[node name="Close" type="Sprite2D" parent="Control/Panel"]
position = Vector2(218.105, -119.789)
texture = ExtResource("11_axj1h")

[node name="CloseButton" type="Button" parent="Control/Panel"]
offset_left = 207.948
offset_top = -132.21
offset_right = 227.947
offset_bottom = -110.21
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="UnlockProgressLabel" parent="Control/Panel" instance=ExtResource("6_c883v")]
offset_left = 20.0
offset_top = 350.0
offset_right = 380.0
offset_bottom = 370.0
text = "Unlock progress will appear here"
