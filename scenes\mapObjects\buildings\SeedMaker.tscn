[gd_scene load_steps=13 format=3 uid="uid://bwd4lrstis71c"]

[ext_resource type="Script" uid="uid://bho7eka1ukcfk" path="res://scenes/mapObjects/buildings/SeedMaker.cs" id="1_script"]
[ext_resource type="Texture2D" uid="uid://dm8suuilxdfoa" path="res://resources/solaria/buildings/seed_making_machine.png" id="2_cbcnx"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_dvujk"]
[ext_resource type="PackedScene" uid="uid://crkjyn76vf7so" path="res://scenes/UI/buildingMenus/SeedMakerMenu.tscn" id="3_gptgy"]
[ext_resource type="PackedScene" uid="uid://b8xf7h2lam3pq" path="res://scenes/UI/progress/ProgressBarVertical.tscn" id="5_kcax7"]

[sub_resource type="Animation" id="Animation_160ic"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SeedMaker:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.5, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SeedMaker:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}

[sub_resource type="Animation" id="Animation_hit"]
resource_name = "hit"
length = 0.2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SeedMaker:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 0.42, 0.27, 1), Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_idle"]
resource_name = "idle"
length = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SeedMaker:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="Animation" id="Animation_working"]
resource_name = "working"
length = 0.16
loop_mode = 1
step = 0.02
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SeedMaker:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SeedMaker:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.02, 0.04, 0.06, 0.08, 0.1, 0.12, 0.14),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(0, 0), Vector2(0.2, 0), Vector2(0, 0), Vector2(-0.2, 0), Vector2(0, 0), Vector2(0.2, 0), Vector2(0, 0), Vector2(-0.2, 0)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("SeedMaker:rotation")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.02, 0.04, 0.06, 0.08, 0.1, 0.118058, 0.14),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0174533, 0.0, -0.0174533, 0.0, 0.0174533, 0.0, -0.0174533]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_grindstone"]
_data = {
&"RESET": SubResource("Animation_160ic"),
&"hit": SubResource("Animation_hit"),
&"idle": SubResource("Animation_idle"),
&"working": SubResource("Animation_working")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_grindstone"]
size = Vector2(14, 10)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_detector"]
size = Vector2(14, 18)

[node name="SeedMaker" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_grindstone")
}
speed_scale = 0.4

[node name="SeedMaker" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0.5, 0)
texture = ExtResource("2_cbcnx")

[node name="CraftingResource" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -5)
scale = Vector2(0.6, 0.75)

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 9)
shape = SubResource("RectangleShape2D_grindstone")

[node name="ProgressBar" parent="." instance=ExtResource("3_dvujk")]
position = Vector2(0, 17)
scale = Vector2(1, 0.6)

[node name="ProgressBarVertical" parent="." instance=ExtResource("5_kcax7")]
position = Vector2(10, 6)
scale = Vector2(0.8, 0.8)

[node name="PlayerDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(0, 6)
shape = SubResource("RectangleShape2D_detector")

[node name="SeedMakerMenu" parent="." instance=ExtResource("3_gptgy")]
